#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة الفصول
Test classes GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from database.db_manager import DatabaseManager
from gui.classes_tab import ClassesTab

def test_classes_gui():
    """اختبار واجهة الفصول"""
    
    print("🧪 اختبار واجهة الفصول")
    print("=" * 50)
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار واجهة الفصول")
    root.geometry("1200x800")
    root.configure(bg='white')
    
    # إعداد قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # اختبار البيانات أولاً
    print("📊 فحص البيانات:")
    try:
        classes = db_manager.get_classes()
        print(f"  ✅ عدد الفصول: {len(classes)}")
        
        if classes:
            print("  📝 عينة من الفصول:")
            for cls in classes[:3]:
                print(f"    • {cls.get('name', 'بدون اسم')}")
        
    except Exception as e:
        print(f"  ❌ خطأ في البيانات: {e}")
        return
    
    # إنشاء تبويب الفصول
    try:
        classes_tab = ClassesTab(root, db_manager)
        print("✅ تم إنشاء واجهة الفصول بنجاح!")
        
        print("📋 يمكنك الآن:")
        print("   • عرض جميع الفصول الموجودة")
        print("   • إضافة فصول جديدة")
        print("   • تعديل الفصول الموجودة")
        print("   • حذف الفصول")
        print("   • البحث والتصفية")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة الفصول: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # تشغيل التطبيق
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
    finally:
        db_manager.close()

if __name__ == "__main__":
    test_classes_gui()
