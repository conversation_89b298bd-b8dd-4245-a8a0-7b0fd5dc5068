#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دالة get_subjects فقط
Test get_subjects function only
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.advanced_db_manager import AdvancedDatabaseManager

def test_subjects_only():
    """اختبار دالة get_subjects فقط"""
    
    print("🧪 اختبار دالة get_subjects")
    print("=" * 40)
    
    try:
        # الاتصال بقاعدة البيانات المتقدمة
        advanced_db = AdvancedDatabaseManager()
        advanced_db.initialize_database()
        
        print("🔗 تم الاتصال بقاعدة البيانات المتقدمة")
        
        # اختبار دالة get_subjects
        print("\n📖 اختبار get_subjects()...")
        subjects = advanced_db.get_subjects()
        print(f"  ✅ عدد المواد: {len(subjects)}")
        
        if subjects:
            print("  📝 عينة من المواد:")
            for i, subject in enumerate(subjects[:5]):
                name = subject.get('name', 'بدون اسم')
                code = subject.get('code', 'بدون رمز')
                is_cultural = subject.get('is_cultural', 0)
                dept_name = subject.get('department_name', 'غير محدد')
                print(f"    {i+1}. {name} | {code} | {'ثقافية' if is_cultural else 'تخصصية'} | {dept_name}")
        else:
            print("  ⚠️ لا توجد مواد")
        
        advanced_db.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار get_subjects: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_subjects_only()
