"""
واجهة تبويب المواد الجديدة (تصميم ديناميكي وحديث)
"""

import tkinter as tk
from tkinter import ttk, messagebox

class SubjectsTabNew:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.setup_ui()
        self.refresh_data()

    def setup_ui(self):
        self.frame = tk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title = tk.Label(self.frame, text="إدارة المواد الدراسية", font=("Cairo", 18, "bold"))
        title.pack(pady=10)

        # نموذج إضافة/تعديل مادة
        form_frame = tk.LabelFrame(self.frame, text="بيانات المادة", padx=10, pady=10)
        form_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(form_frame, text="اسم المادة:").grid(row=0, column=0, sticky=tk.W)
        self.name_entry = tk.Entry(form_frame)
        self.name_entry.grid(row=0, column=1, sticky=tk.W)

        tk.Label(form_frame, text="نوع المادة:").grid(row=1, column=0, sticky=tk.W)
        self.type_combo = ttk.Combobox(form_frame, values=["ثقافية", "تخصصية"], state="readonly")
        self.type_combo.grid(row=1, column=1, sticky=tk.W)
        self.type_combo.current(0)
        self.type_combo.bind("<<ComboboxSelected>>", self.on_type_change)

        tk.Label(form_frame, text="القسم:").grid(row=2, column=0, sticky=tk.W)
        self.department_combo = ttk.Combobox(form_frame, state="readonly")
        self.department_combo.grid(row=2, column=1, sticky=tk.W)

        # إضافة حقول جديدة
        tk.Label(form_frame, text="رمز المادة:").grid(row=3, column=0, sticky=tk.W)
        self.code_entry = tk.Entry(form_frame)
        self.code_entry.grid(row=3, column=1, sticky=tk.W)

        tk.Label(form_frame, text="عدد الوحدات:").grid(row=4, column=0, sticky=tk.W)
        self.units_entry = tk.Entry(form_frame)
        self.units_entry.grid(row=4, column=1, sticky=tk.W)
        self.units_entry.insert(0, "2")  # قيمة افتراضية

        tk.Label(form_frame, text="إجمالي الدرجة:").grid(row=5, column=0, sticky=tk.W)
        self.total_degree_entry = tk.Entry(form_frame)
        self.total_degree_entry.grid(row=5, column=1, sticky=tk.W)
        self.total_degree_entry.insert(0, "100")  # قيمة افتراضية

        tk.Label(form_frame, text="يُضاف للمجموع:").grid(row=6, column=0, sticky=tk.W)
        self.include_total_combo = ttk.Combobox(form_frame, values=["نعم", "لا"], state="readonly")
        self.include_total_combo.grid(row=6, column=1, sticky=tk.W)
        self.include_total_combo.current(0)

        # أزرار
        btn_frame = tk.Frame(form_frame)
        btn_frame.grid(row=7, column=0, columnspan=2, pady=10)
        self.add_btn = tk.Button(btn_frame, text="إضافة", command=self.add_subject, bg="#4CAF50", fg="white", font=("Cairo", 10, "bold"))
        self.add_btn.pack(side=tk.LEFT, padx=5)
        self.update_btn = tk.Button(btn_frame, text="تعديل", command=self.update_subject, state=tk.DISABLED, bg="#2196F3", fg="white", font=("Cairo", 10, "bold"))
        self.update_btn.pack(side=tk.LEFT, padx=5)
        self.clear_btn = tk.Button(btn_frame, text="تفريغ", command=self.clear_form, bg="#FF9800", fg="white", font=("Cairo", 10, "bold"))
        self.clear_btn.pack(side=tk.LEFT, padx=5)

        # جدول عرض المواد
        self.table = ttk.Treeview(self.frame, columns=("id", "name", "code", "department", "type", "units", "total_degree", "include_total"), show="headings")
        self.table.heading("id", text="#")
        self.table.heading("name", text="اسم المادة")
        self.table.heading("code", text="رمز المادة")
        self.table.heading("department", text="القسم")
        self.table.heading("type", text="النوع")
        self.table.heading("units", text="الوحدات")
        self.table.heading("total_degree", text="إجمالي الدرجة")
        self.table.heading("include_total", text="يُضاف للمجموع")

        # تحديد عرض الأعمدة
        self.table.column("id", width=50)
        self.table.column("name", width=200)
        self.table.column("code", width=100)
        self.table.column("department", width=150)
        self.table.column("type", width=100)
        self.table.column("units", width=80)
        self.table.column("total_degree", width=100)
        self.table.column("include_total", width=120)

        self.table.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.table.bind("<ButtonRelease-1>", self.on_row_select)

    def on_type_change(self, event=None):
        """تغيير خيارات القسم حسب نوع المادة"""
        subject_type = self.type_combo.get()

        if subject_type == "ثقافية":
            # للمواد الثقافية، إضافة خيار "جميع التخصصات"
            departments = self.db_manager.get_departments()
            dept_names = [d['name'] for d in departments]
            dept_names.insert(0, "جميع التخصصات")  # إضافة في البداية
            self.department_combo['values'] = dept_names
            self.department_combo.set("جميع التخصصات")  # تحديد كقيمة افتراضية
        else:
            # للمواد التخصصية، عرض الأقسام فقط
            departments = self.db_manager.get_departments()
            dept_names = [d['name'] for d in departments]
            self.department_combo['values'] = dept_names
            if dept_names:
                self.department_combo.set(dept_names[0])  # أول قسم كقيمة افتراضية

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # تحميل الأقسام
            departments = self.db_manager.get_departments()

            # تحديث خيارات القسم حسب نوع المادة المحدد
            self.on_type_change()

            # تحميل المواد
            self.table.delete(*self.table.get_children())

            # استخدام استعلام محسن للحصول على المواد
            cursor = self.db_manager.connection.cursor()
            cursor.execute("""
                SELECT s.id, s.name, s.code, s.department_id, s.subject_category,
                       s.is_cultural, s.units, s.total_degree, s.include_in_total
                FROM subjects s
                ORDER BY s.is_cultural DESC, s.name
            """)
            subjects = cursor.fetchall()

            for sub in subjects:
                subject_id, name, code, dept_id, category, is_cultural, units, total_degree, include_total = sub

                # تحديد اسم القسم
                if is_cultural:
                    dept_name = "جميع التخصصات"
                else:
                    dept_name = next((d['name'] for d in departments if d['id'] == dept_id), "غير محدد")

                # تحديد نوع المادة
                subject_type = "ثقافية" if is_cultural else "تخصصية"

                # تحديد إضافة للمجموع
                include_text = "نعم" if include_total else "لا"

                self.table.insert('', 'end', values=(
                    subject_id, name, code or "", dept_name, subject_type,
                    units or 1, total_degree or 100, include_text
                ))

        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحديث البيانات: {str(e)}")

    def clear_form(self):
        """تفريغ النموذج"""
        self.name_entry.delete(0, tk.END)
        self.code_entry.delete(0, tk.END)
        self.units_entry.delete(0, tk.END)
        self.units_entry.insert(0, "2")
        self.total_degree_entry.delete(0, tk.END)
        self.total_degree_entry.insert(0, "100")
        self.type_combo.current(0)
        self.include_total_combo.current(0)
        self.on_type_change()  # تحديث خيارات القسم
        self.add_btn.config(state=tk.NORMAL)
        self.update_btn.config(state=tk.DISABLED)
        self.selected_id = None

    def add_subject(self):
        """إضافة مادة جديدة مع دعم المواد الثقافية لجميع الأقسام"""
        try:
            # الحصول على البيانات من النموذج
            name = self.name_entry.get().strip()
            code = self.code_entry.get().strip()
            dept_name = self.department_combo.get().strip()
            subject_type = self.type_combo.get().strip()
            include_in_total = self.include_total_combo.get().strip()

            try:
                units = int(self.units_entry.get() or 1)
                total_degree = int(self.total_degree_entry.get() or 100)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للوحدات والدرجة!")
                return

            # التحقق من البيانات المطلوبة
            if not name:
                messagebox.showerror("خطأ", "اسم المادة مطلوب!")
                return

            if not subject_type:
                messagebox.showerror("خطأ", "نوع المادة مطلوب!")
                return

            # تحديد معرف القسم
            department_id = None
            if subject_type == 'تخصصية':
                if not dept_name or dept_name == "جميع التخصصات":
                    messagebox.showerror("خطأ", "يجب اختيار قسم محدد للمواد التخصصية!")
                    return
                departments = self.db_manager.get_departments()
                department_id = next((d['id'] for d in departments if d['name'] == dept_name), None)
                if not department_id:
                    messagebox.showerror("خطأ", "القسم المحدد غير صحيح!")
                    return
            # للمواد الثقافية، department_id يبقى NULL

            # حفظ المادة في قاعدة البيانات
            success = self.save_subject_to_db({
                'name': name,
                'code': code,
                'department_id': department_id,
                'subject_category': subject_type,
                'is_cultural': 1 if subject_type == 'ثقافية' else 0,
                'include_in_total': 1 if include_in_total == 'نعم' else 0,
                'units': units,
                'total_degree': total_degree,
                'description': f"مادة {subject_type}"
            })

            if success:
                # إذا كانت مادة ثقافية، ربطها بجميع الأقسام
                if subject_type == 'ثقافية':
                    self.link_cultural_subject_to_all_departments(name, units)

                messagebox.showinfo("نجح", f"تم إضافة المادة '{name}' بنجاح!")
                self.refresh_data()
                self.clear_form()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المادة! تحقق من أن رمز المادة غير مستخدم.")

        except Exception as e:
            messagebox.showerror("خطأ غير متوقع", f"فشل في إضافة المادة: {str(e)}")
            print(f"خطأ في إضافة المادة: {e}")
            import traceback
            traceback.print_exc()

    def save_subject_to_db(self, subject_data):
        """حفظ المادة في قاعدة البيانات"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()

            cursor = self.db_manager.connection.cursor()

            # التحقق من الأعمدة الموجودة
            cursor.execute("PRAGMA table_info(subjects)")
            columns = [column[1] for column in cursor.fetchall()]

            # بناء استعلام الإدراج
            available_fields = []
            values = []

            field_mapping = {
                'name': subject_data['name'],
                'code': subject_data.get('code'),
                'department_id': subject_data.get('department_id'),
                'subject_category': subject_data.get('subject_category'),
                'is_cultural': subject_data.get('is_cultural'),
                'include_in_total': subject_data.get('include_in_total'),
                'units': subject_data.get('units'),
                'total_degree': subject_data.get('total_degree'),
                'description': subject_data.get('description')
            }

            for field, value in field_mapping.items():
                if field in columns:
                    available_fields.append(field)
                    values.append(value)

            if not available_fields:
                raise Exception("لا توجد حقول صالحة للإدراج")

            placeholders = ', '.join(['?' for _ in available_fields])
            fields_str = ', '.join(available_fields)
            query = f"INSERT INTO subjects ({fields_str}) VALUES ({placeholders})"

            cursor.execute(query, values)
            self.db_manager.connection.commit()

            print(f"✅ تم حفظ المادة: {subject_data['name']}")
            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ المادة: {e}")
            return False

    def link_cultural_subject_to_all_departments(self, subject_name, default_units=2):
        """ربط المادة الثقافية بجميع الأقسام"""
        try:
            cursor = self.db_manager.connection.cursor()

            # الحصول على معرف المادة
            cursor.execute("SELECT id FROM subjects WHERE name = ? AND is_cultural = 1", (subject_name,))
            subject_result = cursor.fetchone()

            if not subject_result:
                print(f"❌ لم يتم العثور على المادة الثقافية: {subject_name}")
                return False

            subject_id = subject_result[0]

            # الحصول على جميع الأقسام
            cursor.execute("SELECT id, name FROM departments")
            departments = cursor.fetchall()

            # ربط المادة بكل قسم
            for dept_id, dept_name in departments:
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO department_subjects
                        (department_id, subject_id, units, is_required, semester, year_level)
                        VALUES (?, ?, ?, 1, 1, 1)
                    """, (dept_id, subject_id, default_units))

                    print(f"  ✅ ربط {subject_name} بـ {dept_name} ({default_units} وحدات)")

                except Exception as e:
                    print(f"  ❌ خطأ في ربط {subject_name} بـ {dept_name}: {e}")

            self.db_manager.connection.commit()
            print(f"✅ تم ربط المادة الثقافية '{subject_name}' بجميع الأقسام")
            return True

        except Exception as e:
            print(f"❌ خطأ في ربط المادة الثقافية: {e}")
            return False

    def on_row_select(self, event):
        """تحديد صف في الجدول"""
        selected = self.table.focus()
        if not selected:
            return

        try:
            values = self.table.item(selected, 'values')
            if len(values) < 8:
                return

            self.selected_id = int(values[0])

            # ملء النموذج بالبيانات
            self.name_entry.delete(0, tk.END)
            self.name_entry.insert(0, values[1])

            self.code_entry.delete(0, tk.END)
            self.code_entry.insert(0, values[2])

            self.department_combo.set(values[3])
            self.type_combo.set(values[4])

            self.units_entry.delete(0, tk.END)
            self.units_entry.insert(0, values[5])

            self.total_degree_entry.delete(0, tk.END)
            self.total_degree_entry.insert(0, values[6])

            self.include_total_combo.set(values[7])

            # تحديث حالة الأزرار
            self.add_btn.config(state=tk.DISABLED)
            self.update_btn.config(state=tk.NORMAL)

        except Exception as e:
            print(f"خطأ في تحديد الصف: {e}")

    def update_subject(self):
        """تعديل مادة موجودة"""
        if not hasattr(self, 'selected_id') or not self.selected_id:
            messagebox.showerror("خطأ", "يرجى اختيار مادة للتعديل.")
            return

        try:
            # الحصول على البيانات من النموذج
            name = self.name_entry.get().strip()
            code = self.code_entry.get().strip()
            dept_name = self.department_combo.get().strip()
            subject_type = self.type_combo.get().strip()
            include_in_total = self.include_total_combo.get().strip()

            try:
                units = int(self.units_entry.get() or 1)
                total_degree = int(self.total_degree_entry.get() or 100)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للوحدات والدرجة!")
                return

            # التحقق من البيانات المطلوبة
            if not name:
                messagebox.showerror("خطأ", "اسم المادة مطلوب!")
                return

            # تحديد معرف القسم
            department_id = None
            if subject_type == 'تخصصية':
                if not dept_name or dept_name == "جميع التخصصات":
                    messagebox.showerror("خطأ", "يجب اختيار قسم محدد للمواد التخصصية!")
                    return
                departments = self.db_manager.get_departments()
                department_id = next((d['id'] for d in departments if d['name'] == dept_name), None)
                if not department_id:
                    messagebox.showerror("خطأ", "القسم المحدد غير صحيح!")
                    return

            # تحديث المادة في قاعدة البيانات
            cursor = self.db_manager.connection.cursor()
            cursor.execute("""
                UPDATE subjects
                SET name = ?, code = ?, department_id = ?, subject_category = ?,
                    is_cultural = ?, include_in_total = ?, units = ?, total_degree = ?
                WHERE id = ?
            """, (
                name, code, department_id, subject_type,
                1 if subject_type == 'ثقافية' else 0,
                1 if include_in_total == 'نعم' else 0,
                units, total_degree, self.selected_id
            ))

            self.db_manager.connection.commit()

            messagebox.showinfo("تم التعديل", "تم تعديل بيانات المادة بنجاح.")
            self.refresh_data()
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تعديل المادة: {str(e)}")
            print(f"خطأ في تعديل المادة: {e}")
