#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة تحديث الطلاب
Test student update functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_student_update():
    """اختبار تحديث بيانات الطالب"""
    
    # إنشاء اتصال بقاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # التحقق من هيكل الجدول
    cursor = db_manager.connection.cursor()
    cursor.execute("PRAGMA table_info(students)")
    columns = cursor.fetchall()
    
    print("هيكل جدول الطلاب:")
    print("=" * 50)
    for column in columns:
        print(f"العمود: {column[1]}, النوع: {column[2]}, مطلوب: {'نعم' if column[3] else 'لا'}")
    
    print("\n" + "=" * 50)
    
    # جلب أول طالب للاختبار
    cursor.execute("SELECT * FROM students LIMIT 1")
    student = cursor.fetchone()
    
    if student:
        print(f"تم العثور على طالب للاختبار: {student}")
        
        # محاولة تحديث بيانات الطالب
        try:
            # تحديث الاسم فقط كاختبار
            cursor.execute("UPDATE students SET first_name = ? WHERE id = ?", 
                          ("اسم محدث للاختبار", student[0]))
            db_manager.connection.commit()
            
            print("تم تحديث الطالب بنجاح!")
            
            # التحقق من التحديث
            cursor.execute("SELECT first_name FROM students WHERE id = ?", (student[0],))
            updated_name = cursor.fetchone()
            print(f"الاسم بعد التحديث: {updated_name[0]}")
            
        except Exception as e:
            print(f"خطأ في التحديث: {str(e)}")
    else:
        print("لا توجد بيانات طلاب للاختبار")
    
    db_manager.close()

if __name__ == "__main__":
    test_student_update()
