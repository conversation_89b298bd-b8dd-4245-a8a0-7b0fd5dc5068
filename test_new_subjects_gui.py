#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة المواد الجديدة
Test new subjects GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from database.db_manager import DatabaseManager
from gui.subjects_tab_new import SubjectsTabNew

def test_subjects_gui():
    """اختبار واجهة المواد"""
    
    print("🧪 اختبار واجهة المواد الجديدة")
    print("=" * 50)
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار واجهة المواد")
    root.geometry("1000x700")
    root.configure(bg='white')
    
    # إعداد قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # إنشاء تبويب المواد
    subjects_tab = SubjectsTabNew(root, db_manager)
    
    print("✅ تم إنشاء واجهة المواد بنجاح!")
    print("📋 يمكنك الآن:")
    print("   • إضافة مواد ثقافية (ستظهر لجميع التخصصات)")
    print("   • إضافة مواد تخصصية (لقسم محدد)")
    print("   • تعديل المواد الموجودة")
    print("   • عرض جميع المواد مع تفاصيلها")
    
    # تشغيل التطبيق
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
    finally:
        db_manager.close()

if __name__ == "__main__":
    test_subjects_gui()
