"""
Advanced Database Manager - Enhanced for Advanced Grading System
Supports:
1. Semester-based grading (First and Second semester)
2. Continuous assessment and terminal exams
3. Subject categorization (counted vs non-counted in total)
4. Units with pass/fail grading
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from database.db_manager import DatabaseManager

class AdvancedDatabaseManager(DatabaseManager):
    def __init__(self, db_path: str = "school_management_advanced.db"):
        """Initialize advanced database manager"""
        super().__init__(db_path)
        
    def create_tables(self):
        """Create all required tables with advanced grading support"""
        
        # Call parent method to create basic tables
        super().create_tables()
        
        # Enhanced subjects table with advanced features
        enhanced_subjects_table = """
        CREATE TABLE IF NOT EXISTS subjects_enhanced (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT NOT NULL UNIQUE,
            department_id INTEGER NOT NULL,
            credits INTEGER DEFAULT 3,
            description TEXT,
            subject_type TEXT DEFAULT 'regular', -- 'regular', 'unit'
            counted_in_total BOOLEAN DEFAULT 1, -- Whether included in GPA calculation
            max_continuous_first REAL DEFAULT 30, -- Max marks for first semester continuous assessment
            max_terminal_first REAL DEFAULT 70, -- Max marks for first semester terminal exam
            max_continuous_second REAL DEFAULT 30, -- Max marks for second semester continuous assessment
            max_terminal_second REAL DEFAULT 70, -- Max marks for second semester terminal exam
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE CASCADE
        )
        """
        
        # Advanced exam results table
        advanced_exam_results_table = """
        CREATE TABLE IF NOT EXISTS exam_results_advanced (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            academic_year TEXT NOT NULL,
            semester INTEGER NOT NULL, -- 1 or 2
            continuous_assessment REAL DEFAULT 0, -- أعمال السنة
            terminal_exam REAL DEFAULT 0, -- درجة الامتحان النهائي
            total_marks REAL GENERATED ALWAYS AS (continuous_assessment + terminal_exam) STORED,
            grade TEXT, -- Letter grade or "جدير"/"غير جدير" for units
            status TEXT DEFAULT 'active', -- active, cancelled, etc.
            remarks TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects_enhanced (id) ON DELETE CASCADE,
            UNIQUE(student_id, subject_id, academic_year, semester)
        )
        """
        
        # Academic years table
        academic_years_table = """
        CREATE TABLE IF NOT EXISTS academic_years (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            year_name TEXT NOT NULL UNIQUE, -- e.g., "2023-2024"
            start_date DATE,
            end_date DATE,
            is_current BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # Student semester summary table
        semester_summary_table = """
        CREATE TABLE IF NOT EXISTS semester_summary (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            academic_year TEXT NOT NULL,
            semester INTEGER NOT NULL,
            total_subjects INTEGER DEFAULT 0,
            passed_subjects INTEGER DEFAULT 0,
            failed_subjects INTEGER DEFAULT 0,
            gpa REAL DEFAULT 0,
            total_marks REAL DEFAULT 0,
            max_possible_marks REAL DEFAULT 0,
            percentage REAL DEFAULT 0,
            rank_in_class INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
            UNIQUE(student_id, academic_year, semester)
        )
        """
        
        # Execute advanced table creation
        advanced_tables = [
            enhanced_subjects_table,
            advanced_exam_results_table,
            academic_years_table, 
            semester_summary_table
        ]
        
        for table_query in advanced_tables:
            self.execute_update(table_query)
            
        # Migrate existing data if needed
        self.migrate_existing_data()
        
    def migrate_existing_data(self):
        """Migrate existing subjects to enhanced subjects table"""
        try:
            # Check if subjects_enhanced is empty and regular subjects exist
            enhanced_count = self.execute_query("SELECT COUNT(*) as count FROM subjects_enhanced")
            regular_subjects = self.execute_query("SELECT * FROM subjects")
            
            if enhanced_count[0]['count'] == 0 and regular_subjects:
                print("Migrating existing subjects to enhanced table...")
                
                for subject in regular_subjects:
                    query = """
                        INSERT INTO subjects_enhanced 
                        (name, code, department_id, credits, description, subject_type, counted_in_total)
                        VALUES (?, ?, ?, ?, ?, 'regular', 1)
                    """
                    self.execute_update(query, (
                        subject['name'],
                        subject['code'], 
                        subject['department_id'],
                        subject['credits'],
                        subject.get('description', '')
                    ))
                    
                print("Migration completed successfully")
                
        except Exception as e:
            print(f"Migration error: {e}")
            
    def insert_default_academic_year(self):
        """Insert current academic year if not exists"""
        current_year = datetime.now().year
        academic_year = f"{current_year}-{current_year + 1}"
        
        existing = self.execute_query(
            "SELECT id FROM academic_years WHERE year_name = ?", 
            (academic_year,)
        )
        
        if not existing:
            query = """
                INSERT INTO academic_years (year_name, start_date, end_date, is_current)
                VALUES (?, ?, ?, 1)
            """
            start_date = f"{current_year}-09-01"
            end_date = f"{current_year + 1}-06-30"
            
            self.execute_update(query, (academic_year, start_date, end_date))
            
    # Enhanced Subject Operations
    def add_enhanced_subject(self, name: str, code: str, department_id: int, 
                           credits: int = 3, description: str = "",
                           subject_type: str = "regular", counted_in_total: bool = True,
                           max_continuous_first: float = 30, max_terminal_first: float = 70,
                           max_continuous_second: float = 30, max_terminal_second: float = 70) -> int:
        """Add a new enhanced subject"""
        query = """
            INSERT INTO subjects_enhanced 
            (name, code, department_id, credits, description, subject_type, counted_in_total,
             max_continuous_first, max_terminal_first, max_continuous_second, max_terminal_second)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        self.execute_update(query, (
            name, code, department_id, credits, description, subject_type, counted_in_total,
            max_continuous_first, max_terminal_first, max_continuous_second, max_terminal_second
        ))
        return self.get_last_insert_id()
        
    def update_enhanced_subject(self, subject_id: int, **kwargs) -> bool:
        """Update an enhanced subject"""
        set_clauses = []
        params = []
        
        allowed_fields = [
            'name', 'code', 'department_id', 'credits', 'description', 
            'subject_type', 'counted_in_total', 'max_continuous_first',
            'max_terminal_first', 'max_continuous_second', 'max_terminal_second'
        ]
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                set_clauses.append(f"{field} = ?")
                params.append(value)
                
        if not set_clauses:
            return False
            
        params.append(subject_id)
        query = f"UPDATE subjects_enhanced SET {', '.join(set_clauses)} WHERE id = ?"
        
        return self.execute_update(query, tuple(params)) > 0
        
    def get_enhanced_subjects(self, department_id: int = None, subject_type: str = None) -> List[Dict]:
        """Get enhanced subjects with filtering options"""
        query = """
            SELECT s.*, d.name as department_name 
            FROM subjects_enhanced s 
            JOIN departments d ON s.department_id = d.id 
            WHERE 1=1
        """
        params = []
        
        if department_id:
            query += " AND s.department_id = ?"
            params.append(department_id)
            
        if subject_type:
            query += " AND s.subject_type = ?"
            params.append(subject_type)
            
        query += " ORDER BY d.name, s.name"
        
        return self.execute_query(query, tuple(params))
        
    # Advanced Exam Results Operations
    def add_advanced_exam_result(self, student_id: int, subject_id: int, 
                               academic_year: str, semester: int,
                               continuous_assessment: float = 0, 
                               terminal_exam: float = 0,
                               remarks: str = "") -> int:
        """Add or update advanced exam result"""
        
        # Get subject info to determine grading type
        subject_info = self.execute_query(
            "SELECT subject_type, max_continuous_first, max_terminal_first, max_continuous_second, max_terminal_second FROM subjects_enhanced WHERE id = ?",
            (subject_id,)
        )
        
        if not subject_info:
            raise ValueError("Subject not found")
            
        subject = subject_info[0]
        
        # Calculate grade based on subject type
        if subject['subject_type'] == 'unit':
            # For units, check if total is >= 50% for pass
            max_continuous = subject['max_continuous_first'] if semester == 1 else subject['max_continuous_second']
            max_terminal = subject['max_terminal_first'] if semester == 1 else subject['max_terminal_second']
            total_possible = max_continuous + max_terminal
            total_obtained = continuous_assessment + terminal_exam
            
            grade = "جدير" if total_obtained >= (total_possible * 0.5) else "غير جدير"
        else:
            # For regular subjects, calculate letter grade
            max_continuous = subject['max_continuous_first'] if semester == 1 else subject['max_continuous_second']
            max_terminal = subject['max_terminal_first'] if semester == 1 else subject['max_terminal_second']
            total_possible = max_continuous + max_terminal
            total_obtained = continuous_assessment + terminal_exam
            
            percentage = (total_obtained / total_possible) * 100 if total_possible > 0 else 0
            grade = self.calculate_letter_grade(percentage)
            
        # Check if result already exists
        existing = self.execute_query(
            "SELECT id FROM exam_results_advanced WHERE student_id = ? AND subject_id = ? AND academic_year = ? AND semester = ?",
            (student_id, subject_id, academic_year, semester)
        )
        
        # Calculate total marks
        total_marks = continuous_assessment + terminal_exam
        
        if existing:
            # Update existing result
            query = """
                UPDATE exam_results_advanced 
                SET continuous_assessment = ?, terminal_exam = ?, total_marks = ?, grade = ?, remarks = ?, updated_at = CURRENT_TIMESTAMP
                WHERE student_id = ? AND subject_id = ? AND academic_year = ? AND semester = ?
            """
            self.execute_update(query, (
                continuous_assessment, terminal_exam, total_marks, grade, remarks,
                student_id, subject_id, academic_year, semester
            ))
            result_id = existing[0]['id']
        else:
            # Insert new result
            query = """
                INSERT INTO exam_results_advanced 
                (student_id, subject_id, academic_year, semester, continuous_assessment, terminal_exam, total_marks, grade, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            self.execute_update(query, (
                student_id, subject_id, academic_year, semester,
                continuous_assessment, terminal_exam, total_marks, grade, remarks
            ))
            result_id = self.get_last_insert_id()
            
        # Update semester summary
        self.update_semester_summary(student_id, academic_year, semester)
        
        return result_id
        
    def calculate_letter_grade(self, percentage: float) -> str:
        """Calculate letter grade from percentage"""
        if percentage >= 95:
            return "A+"
        elif percentage >= 90:
            return "A"
        elif percentage >= 85:
            return "A-"
        elif percentage >= 80:
            return "B+"
        elif percentage >= 75:
            return "B"
        elif percentage >= 70:
            return "B-"
        elif percentage >= 65:
            return "C+"
        elif percentage >= 60:
            return "C"
        elif percentage >= 55:
            return "C-"
        elif percentage >= 50:
            return "D"
        else:
            return "F"
            
    def update_semester_summary(self, student_id: int, academic_year: str, semester: int):
        """Update semester summary for a student"""
        
        # Get all results for this student/semester
        results = self.execute_query("""
            SELECT r.*, s.subject_type, s.counted_in_total, s.credits,
                   s.max_continuous_first, s.max_terminal_first,
                   s.max_continuous_second, s.max_terminal_second
            FROM exam_results_advanced r
            JOIN subjects_enhanced s ON r.subject_id = s.id
            WHERE r.student_id = ? AND r.academic_year = ? AND r.semester = ?
        """, (student_id, academic_year, semester))
        
        if not results:
            return
            
        total_subjects = len(results)
        passed_subjects = 0
        failed_subjects = 0
        total_marks = 0
        max_possible_marks = 0
        total_grade_points = 0
        total_credits = 0
        
        for result in results:
            # Calculate max possible marks for this subject
            if semester == 1:
                max_marks = result['max_continuous_first'] + result['max_terminal_first']
            else:
                max_marks = result['max_continuous_second'] + result['max_terminal_second']
                
            obtained_marks = result['continuous_assessment'] + result['terminal_exam']
            
            total_marks += obtained_marks
            max_possible_marks += max_marks
            
            # Check pass/fail
            if result['subject_type'] == 'unit':
                if result['grade'] == 'جدير':
                    passed_subjects += 1
                else:
                    failed_subjects += 1
            else:
                if result['grade'] != 'F':
                    passed_subjects += 1
                else:
                    failed_subjects += 1
                    
                # Calculate GPA only for counted subjects
                if result['counted_in_total']:
                    percentage = (obtained_marks / max_marks) * 100 if max_marks > 0 else 0
                    grade_point = self.percentage_to_grade_point(percentage)
                    credits = result['credits']
                    
                    total_grade_points += grade_point * credits
                    total_credits += credits
                    
        # Calculate overall metrics
        gpa = total_grade_points / total_credits if total_credits > 0 else 0
        percentage = (total_marks / max_possible_marks) * 100 if max_possible_marks > 0 else 0
        
        # Update or insert summary
        existing = self.execute_query(
            "SELECT id FROM semester_summary WHERE student_id = ? AND academic_year = ? AND semester = ?",
            (student_id, academic_year, semester)
        )
        
        if existing:
            query = """
                UPDATE semester_summary 
                SET total_subjects = ?, passed_subjects = ?, failed_subjects = ?,
                    gpa = ?, total_marks = ?, max_possible_marks = ?, percentage = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE student_id = ? AND academic_year = ? AND semester = ?
            """
            self.execute_update(query, (
                total_subjects, passed_subjects, failed_subjects,
                gpa, total_marks, max_possible_marks, percentage,
                student_id, academic_year, semester
            ))
        else:
            query = """
                INSERT INTO semester_summary 
                (student_id, academic_year, semester, total_subjects, passed_subjects, failed_subjects,
                 gpa, total_marks, max_possible_marks, percentage)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            self.execute_update(query, (
                student_id, academic_year, semester, total_subjects, passed_subjects, failed_subjects,
                gpa, total_marks, max_possible_marks, percentage
            ))
            
    def get_student_semester_results(self, student_id: int, academic_year: str, semester: int) -> Dict:
        """Get comprehensive semester results for a student"""
        
        # Get semester summary
        summary = self.execute_query(
            "SELECT * FROM semester_summary WHERE student_id = ? AND academic_year = ? AND semester = ?",
            (student_id, academic_year, semester)
        )
        
        # Get detailed results
        results = self.execute_query("""
            SELECT r.*, s.name as subject_name, s.code as subject_code, 
                   s.subject_type, s.counted_in_total, s.credits,
                   s.max_continuous_first, s.max_terminal_first,
                   s.max_continuous_second, s.max_terminal_second
            FROM exam_results_advanced r
            JOIN subjects_enhanced s ON r.subject_id = s.id
            WHERE r.student_id = ? AND r.academic_year = ? AND r.semester = ?
            ORDER BY s.name
        """, (student_id, academic_year, semester))
        
        return {
            'summary': summary[0] if summary else None,
            'results': results
        }
        
    def get_class_semester_results(self, department_id: int, academic_year: str, semester: int) -> List[Dict]:
        """Get semester results for all students in a department"""
        
        query = """
            SELECT s.id as student_id, s.first_name, s.last_name, s.national_id,
                   ss.total_subjects, ss.passed_subjects, ss.failed_subjects,
                   ss.gpa, ss.total_marks, ss.max_possible_marks, ss.percentage,
                   d.name as department_name
            FROM students s
            LEFT JOIN semester_summary ss ON s.id = ss.student_id 
                AND ss.academic_year = ? AND ss.semester = ?
            LEFT JOIN departments d ON s.department_id = d.id
            WHERE s.department_id = ?
            ORDER BY ss.percentage DESC NULLS LAST, s.first_name
        """
        
        return self.execute_query(query, (academic_year, semester, department_id))
        
    def get_academic_years(self) -> List[Dict]:
        """Get all academic years"""
        return self.execute_query("SELECT * FROM academic_years ORDER BY year_name DESC")
        
    def set_current_academic_year(self, year_id: int) -> bool:
        """Set the current academic year"""
        # First, unset all current flags
        self.execute_update("UPDATE academic_years SET is_current = 0")
        
        # Then set the selected year as current
        return self.execute_update("UPDATE academic_years SET is_current = 1 WHERE id = ?", (year_id,)) > 0
        
    def get_current_academic_year(self) -> Optional[Dict]:
        """Get the current academic year"""
        result = self.execute_query("SELECT * FROM academic_years WHERE is_current = 1 LIMIT 1")
        return result[0] if result else None
        
    def initialize_database(self):
        """Initialize database with advanced features"""
        super().initialize_database()
        self.insert_default_academic_year()
        
    def add_department(self, name: str) -> int:
        """إضافة قسم جديد (اسم فقط، code = name)"""
        query = "INSERT INTO departments (name, code) VALUES (?, ?)"
        self.execute_update(query, (name, name))
        return self.get_last_insert_id()
    
    def update_department(self, dept: dict) -> bool:
        """تحديث القسم (اسم فقط)"""
        dept_id = dept.get('id')
        name = dept.get('name')
        if not dept_id or not name:
            return False
        # تحديث الاسم والرمز معًا ليبقى code مطابقًا للاسم
        query = "UPDATE departments SET name = ?, code = ? WHERE id = ?"
        return self.execute_update(query, (name, name, dept_id)) > 0

    def get_subjects(self, department_id=None):
        """Get subjects with enhanced support for cultural subjects"""
        try:
            if department_id:
                # Get subjects for specific department including cultural subjects
                query = """
                    SELECT DISTINCT s.id, s.name, s.code, s.department_id,
                           s.subject_category, s.is_cultural, s.include_in_total,
                           s.units, s.total_degree, s.description,
                           CASE
                               WHEN s.is_cultural = 1 THEN 'ثقافية'
                               ELSE d.name
                           END as department_name
                    FROM subjects s
                    LEFT JOIN departments d ON s.department_id = d.id
                    LEFT JOIN department_subjects ds ON s.id = ds.subject_id
                    WHERE (s.department_id = ? OR (s.is_cultural = 1 AND ds.department_id = ?))
                    ORDER BY s.is_cultural DESC, s.name
                """
                return self.execute_query(query, (department_id, department_id))
            else:
                # Get all subjects
                query = """
                    SELECT s.id, s.name, s.code, s.department_id,
                           s.subject_category, s.is_cultural, s.include_in_total,
                           s.units, s.total_degree, s.description,
                           CASE
                               WHEN s.is_cultural = 1 THEN 'ثقافية'
                               ELSE COALESCE(d.name, 'غير محدد')
                           END as department_name
                    FROM subjects s
                    LEFT JOIN departments d ON s.department_id = d.id
                    ORDER BY s.is_cultural DESC, d.name, s.name
                """
                return self.execute_query(query)
        except Exception as e:
            print(f"Error in get_subjects: {e}")
            return []