#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جدول الشعب وإنشاء بيانات تجريبية
Fix sections table and create sample data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def fix_sections_table():
    """إصلاح جدول الشعب وإنشاء بيانات تجريبية"""
    
    print("🔧 إصلاح جدول الشعب")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # 1. فحص بنية جدول sections
        print("📋 فحص بنية جدول sections:")
        cursor.execute("PRAGMA table_info(sections)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        print(f"  الأعمدة الموجودة: {column_names}")
        
        # 2. إنشاء الشعب بالأعمدة الموجودة فقط
        print(f"\n🏫 إنشاء الشعب:")
        
        # الحصول على الأقسام
        cursor.execute("SELECT id, name FROM departments")
        departments = cursor.fetchall()
        
        sections_data = []
        for dept_id, dept_name in departments:
            # إنشاء 3 شعب لكل قسم
            for i in range(1, 4):
                section_name = f"شعبة {i} - {dept_name}"
                
                try:
                    # استخدام الأعمدة الموجودة فقط
                    if 'description' in column_names:
                        cursor.execute("""
                            INSERT INTO sections (name, department_id, description)
                            VALUES (?, ?, ?)
                        """, (section_name, dept_id, f"شعبة رقم {i} لقسم {dept_name}"))
                    else:
                        cursor.execute("""
                            INSERT INTO sections (name, department_id)
                            VALUES (?, ?)
                        """, (section_name, dept_id))
                    
                    section_id = cursor.lastrowid
                    sections_data.append((section_id, section_name, dept_id, dept_name))
                    print(f"  ✅ {section_name} (ID: {section_id})")
                    
                except Exception as e:
                    print(f"  ❌ خطأ في إنشاء {section_name}: {e}")
        
        db_manager.connection.commit()
        
        # 3. فحص بنية جدول classes
        print(f"\n📋 فحص بنية جدول classes:")
        cursor.execute("PRAGMA table_info(classes)")
        class_columns = cursor.fetchall()
        
        class_column_names = [col[1] for col in class_columns]
        print(f"  الأعمدة الموجودة: {class_column_names}")
        
        # 4. إنشاء فصول بسيطة
        print(f"\n🎓 إنشاء الفصول:")
        
        # الحصول على المواد
        cursor.execute("SELECT id, name, is_cultural FROM subjects LIMIT 10")
        subjects = cursor.fetchall()
        
        class_counter = 1
        
        for section_id, section_name, dept_id, dept_name in sections_data[:3]:  # أول 3 شعب فقط
            for subject_id, subject_name, is_cultural in subjects[:5]:  # أول 5 مواد
                class_name = f"فصل {subject_name} - {section_name}"
                
                try:
                    # بناء الاستعلام حسب الأعمدة الموجودة
                    base_fields = ['name', 'section_id', 'subject_id']
                    base_values = [class_name, section_id, subject_id]
                    
                    optional_fields = {
                        'teacher_name': f"د. مدرس {subject_name}",
                        'schedule': f"الأحد والثلاثاء {8 + (class_counter % 4)}:00",
                        'room_number': f"قاعة {100 + class_counter}",
                        'semester': "الفصل الأول",
                        'academic_year': "2024-2025",
                        'description': f"فصل {subject_name}"
                    }
                    
                    # إضافة الحقول الاختيارية الموجودة
                    for field, value in optional_fields.items():
                        if field in class_column_names:
                            base_fields.append(field)
                            base_values.append(value)
                    
                    # بناء الاستعلام
                    fields_str = ', '.join(base_fields)
                    placeholders = ', '.join(['?' for _ in base_fields])
                    query = f"INSERT INTO classes ({fields_str}) VALUES ({placeholders})"
                    
                    cursor.execute(query, base_values)
                    
                    print(f"  ✅ {class_name}")
                    class_counter += 1
                    
                except Exception as e:
                    print(f"  ❌ خطأ في إنشاء {class_name}: {e}")
        
        db_manager.connection.commit()
        
        # 5. عرض الإحصائيات النهائية
        print(f"\n📊 الإحصائيات النهائية:")
        
        cursor.execute("SELECT COUNT(*) FROM sections")
        sections_count = cursor.fetchone()[0]
        print(f"  • إجمالي الشعب: {sections_count}")
        
        cursor.execute("SELECT COUNT(*) FROM classes")
        classes_count = cursor.fetchone()[0]
        print(f"  • إجمالي الفصول: {classes_count}")
        
        # 6. اختبار استعلام get_classes
        print(f"\n🧪 اختبار استعلام get_classes:")
        try:
            classes = db_manager.get_classes()
            print(f"  ✅ دالة get_classes: {len(classes)} فصل")
            
            if classes:
                print("  📝 عينة من الفصول:")
                for cls in classes[:3]:
                    print(f"    • {cls.get('name', 'بدون اسم')} | {cls.get('section_name', 'بدون شعبة')} | {cls.get('subject_name', 'بدون مادة')}")
            
        except Exception as e:
            print(f"  ❌ خطأ في دالة get_classes: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n" + "=" * 50)
        print("✅ تم إصلاح جدول الشعب وإنشاء البيانات بنجاح!")
        print("🎯 يمكنك الآن الدخول على تبويب الفصول")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    fix_sections_table()
