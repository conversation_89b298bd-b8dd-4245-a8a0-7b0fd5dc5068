#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دوال محسنة لإدارة المواد
Enhanced functions for subjects management
"""

def add_subject_enhanced(self):
    """دالة محسنة لإضافة المواد مع دعم المواد الثقافية لجميع الأقسام"""
    try:
        # الحصول على البيانات من النموذج
        values = self.form.get_values() if hasattr(self, 'form') and self.form else {}
        
        name = values.get('اسم المادة', '').strip()
        code = values.get('رمز المادة', '').strip()
        department = values.get('القسم', '').strip()
        subject_type = values.get('نوع المادة', '').strip()
        include_in_total = values.get('يُضاف للمجموع', 'نعم').strip()
        units = int(values.get('عدد الوحدات', 1) or 1)
        total_degree = int(values.get('إجمالي الدرجة', 100) or 100)
        description = values.get('وصف المادة', '').strip()

        # التحقق من البيانات المطلوبة
        if not name:
            from tkinter import messagebox
            messagebox.showerror("خطأ", "اسم المادة مطلوب!")
            return False

        if not subject_type:
            from tkinter import messagebox
            messagebox.showerror("خطأ", "نوع المادة مطلوب!")
            return False

        # تحديد معرف القسم
        department_id = None
        if subject_type == 'تخصصية':
            if not department or department == "جميع التخصصات":
                from tkinter import messagebox
                messagebox.showerror("خطأ", "يجب اختيار قسم محدد للمواد التخصصية!")
                return False
            department_id = self.get_department_id(department)
            if not department_id:
                from tkinter import messagebox
                messagebox.showerror("خطأ", "القسم المحدد غير صحيح!")
                return False
        # للمواد الثقافية، department_id يبقى NULL

        # بيانات المادة
        subject_data = {
            'name': name,
            'code': code,
            'department_id': department_id,  # NULL للمواد الثقافية
            'subject_category': subject_type,
            'is_cultural': 1 if subject_type == 'ثقافية' else 0,
            'include_in_total': 1 if include_in_total == 'نعم' else 0,
            'units': units,
            'total_degree': total_degree,
            'description': description
        }

        # حفظ المادة
        success = self.save_subject_to_db_enhanced(subject_data)
        
        if success:
            # إذا كانت مادة ثقافية، ربطها بجميع الأقسام
            if subject_type == 'ثقافية':
                self.link_cultural_subject_to_all_departments(name, units)
            
            from tkinter import messagebox
            messagebox.showinfo("نجح", f"تم إضافة المادة '{name}' بنجاح!")
            
            # تنظيف النموذج وتحديث البيانات
            if hasattr(self, 'clear_form'):
                self.clear_form()
            if hasattr(self, 'refresh_data'):
                self.refresh_data()
            return True
        else:
            from tkinter import messagebox
            messagebox.showerror("خطأ", "فشل في إضافة المادة! تحقق من أن رمز المادة غير مستخدم.")
            return False
            
    except Exception as e:
        from tkinter import messagebox
        messagebox.showerror("خطأ غير متوقع", f"فشل في إضافة المادة: {str(e)}")
        return False

def save_subject_to_db_enhanced(self, subject_data):
    """دالة محسنة لحفظ المادة في قاعدة البيانات"""
    try:
        if not self.db_manager.connection:
            self.db_manager.connect()

        cursor = self.db_manager.connection.cursor()
        
        # التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(subjects)")
        columns = [column[1] for column in cursor.fetchall()]

        # بناء استعلام الإدراج
        available_fields = []
        values = []

        field_mapping = {
            'name': subject_data['name'],
            'code': subject_data.get('code'),
            'department_id': subject_data.get('department_id'),
            'subject_category': subject_data.get('subject_category'),
            'is_cultural': subject_data.get('is_cultural'),
            'include_in_total': subject_data.get('include_in_total'),
            'units': subject_data.get('units'),
            'total_degree': subject_data.get('total_degree'),
            'description': subject_data.get('description')
        }

        for field, value in field_mapping.items():
            if field in columns:
                available_fields.append(field)
                values.append(value)

        if not available_fields:
            raise Exception("لا توجد حقول صالحة للإدراج")

        placeholders = ', '.join(['?' for _ in available_fields])
        fields_str = ', '.join(available_fields)
        query = f"INSERT INTO subjects ({fields_str}) VALUES ({placeholders})"

        cursor.execute(query, values)
        self.db_manager.connection.commit()

        print(f"✅ تم حفظ المادة: {subject_data['name']}")
        return True

    except Exception as e:
        print(f"❌ خطأ في حفظ المادة: {e}")
        return False

def link_cultural_subject_to_all_departments(self, subject_name, default_units=2):
    """ربط المادة الثقافية بجميع الأقسام"""
    try:
        cursor = self.db_manager.connection.cursor()
        
        # الحصول على معرف المادة
        cursor.execute("SELECT id FROM subjects WHERE name = ? AND is_cultural = 1", (subject_name,))
        subject_result = cursor.fetchone()
        
        if not subject_result:
            print(f"❌ لم يتم العثور على المادة الثقافية: {subject_name}")
            return False
        
        subject_id = subject_result[0]
        
        # الحصول على جميع الأقسام
        cursor.execute("SELECT id, name FROM departments")
        departments = cursor.fetchall()
        
        # تحديد عدد الوحدات حسب القسم والمادة
        units_mapping = {
            "اللغة العربية": {"default": 2},
            "اللغة الإنجليزية": {"Computer Science": 3, "default": 2},
            "الرياضيات": {"Computer Science": 4, "Electrical Engineering": 4, "default": 3},
            "الفيزياء": {"Electrical Engineering": 3, "Mechanical Engineering": 3, "default": 2},
            "الكيمياء": {"Civil Engineering": 3, "Computer Science": 1, "default": 2},
            "التربية الدينية": {"default": 1},
            "التربية الرياضية": {"default": 1}
        }
        
        # ربط المادة بكل قسم
        for dept_id, dept_name in departments:
            # تحديد عدد الوحدات
            subject_units = units_mapping.get(subject_name, {})
            units = subject_units.get(dept_name, subject_units.get("default", default_units))
            
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO department_subjects 
                    (department_id, subject_id, units, is_required, semester, year_level)
                    VALUES (?, ?, ?, 1, 1, 1)
                """, (dept_id, subject_id, units))
                
                print(f"  ✅ ربط {subject_name} بـ {dept_name} ({units} وحدات)")
                
            except Exception as e:
                print(f"  ❌ خطأ في ربط {subject_name} بـ {dept_name}: {e}")
        
        self.db_manager.connection.commit()
        print(f"✅ تم ربط المادة الثقافية '{subject_name}' بجميع الأقسام")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ربط المادة الثقافية: {e}")
        return False

def get_subjects_by_department(self, department_id=None):
    """الحصول على المواد حسب القسم"""
    try:
        cursor = self.db_manager.connection.cursor()
        
        if department_id:
            # مواد القسم المحدد (تخصصية + ثقافية)
            query = """
                SELECT DISTINCT s.id, s.name, s.code, s.subject_category, s.is_cultural, 
                       s.include_in_total, ds.units, s.total_degree
                FROM subjects s
                LEFT JOIN department_subjects ds ON s.id = ds.subject_id
                WHERE (s.department_id = ? AND s.is_cultural = 0) 
                   OR (s.is_cultural = 1 AND ds.department_id = ?)
                ORDER BY s.is_cultural DESC, s.name
            """
            cursor.execute(query, (department_id, department_id))
        else:
            # جميع المواد
            query = """
                SELECT s.id, s.name, s.code, s.subject_category, s.is_cultural, 
                       s.include_in_total, s.units, s.total_degree
                FROM subjects s
                ORDER BY s.is_cultural DESC, s.name
            """
            cursor.execute(query)
        
        return cursor.fetchall()
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على المواد: {e}")
        return []

# دالة لاختبار النظام الجديد
def test_enhanced_system():
    """اختبار النظام المحسن"""
    print("🧪 اختبار النظام المحسن للمواد")
    print("=" * 50)
    
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from database.db_manager import DatabaseManager
    
    # محاكاة كلاس إدارة المواد
    class MockSubjectsManager:
        def __init__(self):
            self.db_manager = DatabaseManager()
            self.db_manager.connect()
        
        def get_department_id(self, department_name):
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT id FROM departments WHERE name = ?", (department_name,))
            result = cursor.fetchone()
            return result[0] if result else None
        
        # إضافة الدوال المحسنة
        save_subject_to_db_enhanced = save_subject_to_db_enhanced
        link_cultural_subject_to_all_departments = link_cultural_subject_to_all_departments
        get_subjects_by_department = get_subjects_by_department
    
    manager = MockSubjectsManager()
    
    # اختبار إضافة مادة ثقافية
    print("📝 اختبار إضافة مادة ثقافية...")
    cultural_data = {
        'name': 'الفلسفة',
        'code': 'PH101',
        'department_id': None,
        'subject_category': 'ثقافية',
        'is_cultural': 1,
        'include_in_total': 1,
        'units': 2,
        'total_degree': 100,
        'description': 'مادة الفلسفة العامة'
    }
    
    success = manager.save_subject_to_db_enhanced(cultural_data)
    if success:
        manager.link_cultural_subject_to_all_departments('الفلسفة', 2)
    
    # اختبار إضافة مادة تخصصية
    print("\n📝 اختبار إضافة مادة تخصصية...")
    cs_dept_id = manager.get_department_id('Computer Science')
    if cs_dept_id:
        specialized_data = {
            'name': 'أمن المعلومات',
            'code': 'CS501',
            'department_id': cs_dept_id,
            'subject_category': 'تخصصية',
            'is_cultural': 0,
            'include_in_total': 1,
            'units': 3,
            'total_degree': 100,
            'description': 'مادة أمن المعلومات المتقدمة'
        }
        manager.save_subject_to_db_enhanced(specialized_data)
    
    # عرض النتائج
    print(f"\n📊 مواد قسم Computer Science:")
    subjects = manager.get_subjects_by_department(cs_dept_id)
    for subject in subjects[:10]:  # أول 10 مواد
        name = subject[1]
        category = "ثقافية" if subject[4] else "تخصصية"
        units = subject[6] if subject[6] else "غير محدد"
        print(f"  • {name} ({category}) - {units} وحدات")
    
    manager.db_manager.close()
    print(f"\n✅ انتهى الاختبار!")

if __name__ == "__main__":
    test_enhanced_system()
