#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب المواد الحديث المحسن
Modern Enhanced Subjects Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox
from gui.modern_theme import ModernTheme
from gui.components import ModernForm, ModernTable

theme = ModernTheme()

class ModernSubjectsTab:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.form = None
        self.table = None
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)

        self.setup_ui()
        self.load_subjects()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=theme.colors['bg_primary'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # رأس القسم
        self.setup_header(self.main_frame)

        # المحتوى الرئيسي
        content_frame = tk.Frame(self.main_frame, bg=theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=(0, 30))

        # تقسيم المحتوى إلى جانبين
        left_frame = tk.Frame(content_frame, bg=theme.colors['bg_primary'])
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))

        right_frame = tk.Frame(content_frame, bg=theme.colors['bg_primary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(15, 0))

        # النموذج (الجانب الأيسر)
        self.setup_form_section(left_frame)

        # الجدول والبحث (الجانب الأيمن)
        self.setup_table_section(right_frame)
        
    def setup_header(self, parent):
        """إعداد رأس القسم"""
        header_frame = tk.Frame(parent, bg=theme.colors['bg_card'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        content_frame = tk.Frame(header_frame, bg=theme.colors['bg_card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # العنوان والوصف
        title_frame = tk.Frame(content_frame, bg=theme.colors['bg_card'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        title_label = tk.Label(title_frame,
                              text="إدارة المواد",
                              font=theme.fonts['heading_1'],
                              bg=theme.colors['bg_card'],
                              fg=theme.colors['text_primary'])
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(title_frame,
                                text="إضافة وتعديل وإدارة مواد الدراسة",
                                font=theme.fonts['body_medium'],
                                bg=theme.colors['bg_card'],
                                fg=theme.colors['text_secondary'])
        subtitle_label.pack(anchor=tk.W)
        
        # أزرار الإجراءات السريعة
        actions_frame = tk.Frame(content_frame, bg=theme.colors['bg_card'])
        actions_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        actions = [
            {'icon': '📊', 'text': 'إحصائيات', 'command': self.show_statistics, 'color': 'info'},
            {'icon': '📤', 'text': 'تصدير', 'command': self.export_subjects, 'color': 'success'},
            {'icon': '🔄', 'text': 'تحديث', 'command': self.refresh_data, 'color': 'secondary'},
        ]
        
        buttons_frame = tk.Frame(actions_frame, bg=theme.colors['bg_card'])
        buttons_frame.pack(fill=tk.BOTH, expand=True)
        
        for i, action in enumerate(actions):
            btn = ActionButton(buttons_frame, **action)
            btn.grid(row=0, column=i, padx=5, sticky='nsew')
            buttons_frame.grid_columnconfigure(i, weight=1)
            
    def setup_form_section(self, parent):
        """إعداد قسم النموذج"""
        # نموذج إضافة/تعديل المادة
        self.form = ModernForm(parent, title="بيانات المادة")
        self.form.pack(fill=tk.X, pady=(0, 20))

        # إضافة الحقول المطلوبة - محسن للنظام الجديد
        self.form.add_field("اسم المادة", "entry", width=25)
        self.form.add_field("رمز المادة", "entry", width=15)
        departments = self.get_departments_list()
        self.form.add_field("القسم", "combobox", options=departments, width=22)
        self.form.add_field("نوع المادة", "combobox", options=["ثقافية", "تخصصية"], width=22, on_change=self.on_subject_type_change)
        self.form.add_field("يُضاف للمجموع", "combobox", options=["نعم", "لا"], width=15)
        self.form.add_field("عدد الوحدات", "entry", width=10)
        self.form.add_field("إجمالي الدرجة", "entry", width=10)
        # حقول أعمال السنة ودرجات الترمين تظهر فقط إذا كانت المادة ثقافية أو لا تضاف للمجموع
        self.year_work_1_field = self.form.add_field("أعمال السنة (ترم 1)", "entry", width=10)
        self.term_1_field = self.form.add_field("درجة الترم الأول", "entry", width=10)
        self.year_work_2_field = self.form.add_field("أعمال السنة (ترم 2)", "entry", width=10)
        self.term_2_field = self.form.add_field("درجة الترم الثاني", "entry", width=10)
        self.form.add_field("وصف المادة", "text", width=40, height=3)

        # أزرار العمليات
        self.setup_form_buttons(parent)
        
    def on_subject_type_change(self, *_):
        subject_type = self.form.get_value("نوع المادة")
        show = subject_type in ["ثقافية", "لا تضاف للمجموع"]
        for field in [self.year_work_1_field, self.term_1_field, self.year_work_2_field, self.term_2_field]:
            if show:
                field.widget.grid()  # إظهار الحقل
            else:
                field.widget.grid_remove()  # إخفاء الحقل
    
    def setup_form_buttons(self, parent):
        """إعداد أزرار النموذج"""
        buttons_card = ModernCard(parent, title="العمليات")
        buttons_card.pack(fill=tk.X, pady=10)
        
        buttons_frame = tk.Frame(buttons_card.content_frame, bg=theme.colors['bg_card'])
        buttons_frame.pack(fill=tk.X, pady=10)
        
        # إنشاء الأزرار
        self.add_btn = tk.Button(buttons_frame, 
                                text="➕ إضافة مادة", 
                                command=self.add_subject,
                                bg=theme.colors['success'],
                                fg='white',
                                font=theme.fonts['body_medium_bold'],
                                relief='flat',
                                bd=0,
                                padx=15,
                                pady=8,
                                cursor='hand2')
        self.add_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)
        
        self.update_btn = tk.Button(buttons_frame, 
                                   text="✏️ تعديل مادة", 
                                   command=self.update_subject,
                                   bg=theme.colors['warning'],
                                   fg='white',
                                   font=theme.fonts['body_medium_bold'],
                                   relief='flat',
                                   bd=0,
                                   padx=15,
                                   pady=8,
                                   cursor='hand2')
        self.update_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)
        
        self.delete_btn = tk.Button(buttons_frame, 
                                   text="🗑️ حذف مادة", 
                                   command=self.delete_subject,
                                   bg=theme.colors['danger'],
                                   fg='white',
                                   font=theme.fonts['body_medium_bold'],
                                   relief='flat',
                                   bd=0,
                                   padx=15,
                                   pady=8,
                                   cursor='hand2')
        self.delete_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)
        
        self.clear_btn = tk.Button(buttons_frame, 
                                  text="🧹 مسح النموذج", 
                                  command=self.clear_form,
                                  bg=theme.colors['secondary'],
                                  fg='white',
                                  font=theme.fonts['body_medium_bold'],
                                  relief='flat',
                                  bd=0,
                                  padx=15,
                                  pady=8,
                                  cursor='hand2')
        self.clear_btn.pack(side=tk.LEFT, pady=5)
        
    def setup_table_section(self, parent):
        """إعداد قسم الجدول"""
        # بطاقة البحث
        search_card = ModernCard(parent, title="البحث والتصفية")
        search_card.pack(fill=tk.X, pady=(0, 20))
        
        search_frame = tk.Frame(search_card.content_frame, bg=theme.colors['bg_card'])
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        search_label = tk.Label(search_frame,
                               text="البحث:",
                               font=theme.fonts['body_medium_bold'],
                               bg=theme.colors['bg_card'],
                               fg=theme.colors['text_primary'])
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=theme.fonts['body_medium'],
                               bg=theme.colors['bg_card'],
                               fg=theme.colors['text_primary'],
                               relief='solid',
                               bd=1,
                               width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_subjects)
        
        # بطاقة الجدول
        table_card = ModernCard(parent, title="قائمة المواد")
        table_card.pack(fill=tk.BOTH, expand=True)
        
        # الجدول
        columns = ['ID', 'اسم المادة', 'القسم', 'نوع المادة']
        self.table = ModernTable(table_card.content_frame, columns, height=15)
        self.table.pack(fill=tk.BOTH, expand=True)
        
        # ربط حدث التحديد
        self.table.tree.bind('<<TreeviewSelect>>', self.on_subject_select)
        
    def get_departments_list(self):
        """الحصول على قائمة الأقسام"""
        try:
            departments = self.db_manager.get_departments()
            return [dept['name'] for dept in departments]
        except:
            return []
    
    def get_department_id(self, department_name):
        """الحصول على معرف القسم من اسمه"""
        try:
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept['name'] == department_name:
                    return dept['id']
            return None
        except:
            return None
    
    def get_department_name(self, department_id):
        """الحصول على اسم القسم من معرفه"""
        try:
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept['id'] == department_id:
                    return dept['name']
            return ''
        except:
            return ''
    
    def add_subject(self):
        """إضافة مادة جديدة مع النظام المحسن"""
        try:
            values = self.form.get_values()
            name = values.get('اسم المادة', '').strip()
            code = values.get('رمز المادة', '').strip()
            department = values.get('القسم', '').strip()
            subject_type = values.get('نوع المادة', '').strip()
            include_in_total = values.get('يُضاف للمجموع', 'نعم').strip()
            units = int(values.get('عدد الوحدات', 1) or 1)
            total_degree = int(values.get('إجمالي الدرجة', 100) or 100)
            year_work_1 = int(values.get('أعمال السنة (ترم 1)', 0) or 0)
            term_1 = int(values.get('درجة الترم الأول', 0) or 0)
            year_work_2 = int(values.get('أعمال السنة (ترم 2)', 0) or 0)
            term_2 = int(values.get('درجة الترم الثاني', 0) or 0)
            description = values.get('وصف المادة', '').strip()

            # التحقق من البيانات المطلوبة
            if not name:
                messagebox.showerror("خطأ", "اسم المادة مطلوب!")
                return

            if not subject_type:
                messagebox.showerror("خطأ", "نوع المادة مطلوب!")
                return

            # تحديد معرف القسم (اختياري للمواد الثقافية)
            department_id = None
            if department and department != "جميع التخصصات":
                department_id = self.get_department_id(department)
                if not department_id and subject_type == 'تخصصية':
                    messagebox.showerror("خطأ", "القسم مطلوب للمواد التخصصية!")
                    return

            subject_data = {
                'name': name,
                'code': code,
                'department_id': department_id,
                'subject_category': subject_type,
                'is_cultural': 1 if subject_type == 'ثقافية' else 0,
                'include_in_total': 1 if include_in_total == 'نعم' else 0,
                'units': units,
                'total_degree': total_degree,
                'year_work_1': year_work_1,
                'term_1': term_1,
                'year_work_2': year_work_2,
                'term_2': term_2,
                'description': description
            }

            success = self.save_subject_to_db(subject_data)
            if success:
                messagebox.showinfo("نجح", f"تم إضافة المادة '{name}' بنجاح!")
                self.clear_form()
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المادة! ربما رمز المادة مستخدم بالفعل.")
        except Exception as e:
            messagebox.showerror("خطأ غير متوقع", f"فشل في إضافة المادة: {str(e)}")
    
    def save_subject_to_db(self, subject_data):
        """حفظ المادة في قاعدة البيانات مع النظام المحسن"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()

            # التحقق من الأعمدة الموجودة في الجدول
            cursor = self.db_manager.connection.cursor()
            cursor.execute("PRAGMA table_info(subjects)")
            columns = [column[1] for column in cursor.fetchall()]

            # بناء استعلام الإدراج بناءً على الأعمدة الموجودة
            available_fields = []
            values = []

            field_mapping = {
                'name': subject_data['name'],
                'code': subject_data.get('code'),
                'department_id': subject_data.get('department_id'),
                'subject_category': subject_data.get('subject_category'),
                'is_cultural': subject_data.get('is_cultural'),
                'include_in_total': subject_data.get('include_in_total'),
                'units': subject_data.get('units'),
                'total_degree': subject_data.get('total_degree'),
                'year_work_1': subject_data.get('year_work_1'),
                'term_1': subject_data.get('term_1'),
                'year_work_2': subject_data.get('year_work_2'),
                'term_2': subject_data.get('term_2'),
                'description': subject_data.get('description'),
                'type': subject_data.get('subject_category')  # للتوافق مع النظام القديم
            }

            for field, value in field_mapping.items():
                if field in columns and value is not None:
                    available_fields.append(field)
                    values.append(value)

            if not available_fields:
                raise Exception("لا توجد حقول صالحة للإدراج")

            placeholders = ', '.join(['?' for _ in available_fields])
            fields_str = ', '.join(available_fields)
            query = f"INSERT INTO subjects ({fields_str}) VALUES ({placeholders})"

            cursor.execute(query, values)
            self.db_manager.connection.commit()

            print(f"تم إضافة المادة بنجاح: {subject_data['name']}")
            return True

        except Exception as e:
            print(f"خطأ في حفظ المادة: {e}")
            return False
    
    def update_subject(self):
        """تعديل المادة المحددة مع التحقق المحسن"""
        selected = self.table.tree.selection()
        if not selected:
            messagebox.showerror("خطأ في التحديد", "يرجى تحديد مادة للتعديل!")
            return
        try:
            item = self.table.tree.item(selected[0])
            subject_id = item['values'][0]
            values = self.form.get_values()
            name = values.get('اسم المادة', '').strip()
            department = values.get('القسم', '').strip()
            subject_type = values.get('نوع المادة', '').strip()
            year_work_1 = int(values.get('أعمال السنة (ترم 1)', 0) or 0)
            term_1 = int(values.get('درجة الترم الأول', 0) or 0)
            year_work_2 = int(values.get('أعمال السنة (ترم 2)', 0) or 0)
            term_2 = int(values.get('درجة الترم الثاني', 0) or 0)
            department_id = self.get_department_id(department)
            subject_data = {
                'id': subject_id,
                'name': name,
                'department_id': department_id,
                'type': subject_type,
                'year_work_1': year_work_1,
                'term_1': term_1,
                'year_work_2': year_work_2,
                'term_2': term_2
            }
            success = self.update_subject_in_db(subject_data)
            if success:
                messagebox.showinfo("نجح", f"تم تعديل المادة '{name}' بنجاح!")
                self.clear_form()
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في تعديل المادة! ربما رمز المادة مستخدم بالفعل.")
        except Exception as e:
            messagebox.showerror("خطأ غير متوقع", f"فشل في تعديل المادة: {str(e)}")
    
    def update_subject_in_db(self, subject_data):
        """تحديث المادة في قاعدة البيانات"""
        try:
            if hasattr(self.db_manager, 'update_subject'):
                return self.db_manager.update_subject(
                    subject_id=subject_data['id'],
                    name=subject_data['name'],
                    department_id=subject_data['department_id'],
                    type=subject_data['type'],
                    year_work_1=subject_data['year_work_1'],
                    term_1=subject_data['term_1'],
                    year_work_2=subject_data['year_work_2'],
                    term_2=subject_data['term_2']
                )
            else:
                if not self.db_manager.connection:
                    self.db_manager.connect()
                query = "UPDATE subjects SET name=?, department_id=?, type=?, year_work_1=?, term_1=?, year_work_2=?, term_2=? WHERE id=?"
                cursor = self.db_manager.connection.cursor()
                cursor.execute(query, (
                    subject_data['name'],
                    subject_data['department_id'],
                    subject_data['type'],
                    subject_data['year_work_1'],
                    subject_data['term_1'],
                    subject_data['year_work_2'],
                    subject_data['term_2'],
                    subject_data['id']
                ))
                self.db_manager.connection.commit()
                return True
        except Exception as e:
            print(f"خطأ في تحديث المادة: {e}")
            return False
            
    def delete_subject(self):
        """حذف المادة المحددة"""
        selected = self.table.tree.selection()
        if not selected:
            messagebox.showerror("خطأ", "يرجى تحديد مادة للحذف!")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه المادة؟"):
            try:
                item = self.table.tree.item(selected[0])
                subject_id = item['values'][0]
                
                success = self.delete_subject_from_db(subject_id)
                if success:
                    messagebox.showinfo("نجح", "تم حذف المادة بنجاح!")
                    self.refresh_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المادة!")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المادة: {str(e)}")
    
    def delete_subject_from_db(self, subject_id):
        """حذف المادة من قاعدة البيانات"""
        try:
            if hasattr(self.db_manager, 'delete_subject'):
                return self.db_manager.delete_subject(subject_id)
            else:
                # طريقة بديلة
                if not self.db_manager.connection:
                    self.db_manager.connect()
                
                query = "DELETE FROM subjects WHERE id = ?"
                cursor = self.db_manager.connection.cursor()
                cursor.execute(query, (subject_id,))
                self.db_manager.connection.commit()
                return True
                
        except Exception as e:
            print(f"خطأ في حذف المادة: {e}")
            return False
                
    def clear_form(self):
        """مسح النموذج"""
        self.form.clear()
        
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            self.table.clear()
            subjects = self.get_subjects_from_db()
            for subject in subjects:
                department_name = self.get_department_name(subject.get('department_id'))
                self.table.insert_row([
                    subject.get('id', ''),
                    subject.get('name', ''),
                    department_name,
                    subject.get('type', '')
                ])
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def get_subjects_from_db(self):
        """جلب المواد من قاعدة البيانات"""
        try:
            if hasattr(self.db_manager, 'get_subjects'):
                return self.db_manager.get_subjects()
            else:
                # طريقة بديلة
                if not self.db_manager.connection:
                    self.db_manager.connect()

                cursor = self.db_manager.connection.cursor()
                cursor.execute("SELECT id, name, department_id, type, year_work_1, term_1, year_work_2, term_2 FROM subjects ORDER BY id DESC")
                rows = cursor.fetchall()

                subjects = []
                for row in rows:
                    subjects.append({
                        'id': row[0],
                        'name': row[1],
                        'department_id': row[2],
                        'type': row[3],
                        'year_work_1': row[4],
                        'term_1': row[5],
                        'year_work_2': row[6],
                        'term_2': row[7]
                    })

                return subjects

        except Exception as e:
            print(f"خطأ في جلب المواد: {e}")
            return []
    
    def on_subject_select(self, event):
        """معالج تحديد المادة من الجدول"""
        try:
            selected = self.table.tree.selection()
            if selected:
                item = self.table.tree.item(selected[0])
                values = item['values']

                self.form.set_value("اسم المادة", values[1] if len(values) > 1 else '')
                self.form.set_value("القسم", values[2] if len(values) > 2 else '')
                self.form.set_value("نوع المادة", values[3] if len(values) > 3 else '')
        except Exception as e:
            print(f"خطأ في تحديد المادة: {e}")
    
    def get_subject_description(self, subject_id):
        """جلب وصف المادة"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
                
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT description FROM subjects WHERE id = ?", (subject_id,))
            result = cursor.fetchone()
            
            return result[0] if result and result[0] else ''
            
        except Exception as e:
            print(f"خطأ في جلب وصف المادة: {e}")
            return ''
    
    def filter_subjects(self, event=None):
        """تصفية المواد أثناء الكتابة"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.refresh_data()
            return

        try:
            # مسح الجدول الحالي
            self.table.clear()

            # جلب المواد والتصفية
            subjects = self.get_subjects_from_db()

            for subject in subjects:
                if search_term.lower() in subject.get('name', '').lower():
                    department_name = self.get_department_name(subject.get('department_id'))
                    self.table.insert_row([
                        subject.get('id', ''),
                        subject.get('name', ''),
                        department_name,
                        subject.get('type', '')
                    ])
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def show_statistics(self):
        """عرض إحصائيات المواد"""
        try:
            subjects = self.get_subjects_from_db()
            total_subjects = len(subjects)
            
            # إحصائيات حسب النوع
            theoretical = len([s for s in subjects if s.get('type') == 'نظري'])
            practical = len([s for s in subjects if s.get('type') == 'عملي'])
            mixed = len([s for s in subjects if s.get('type') == 'نظري وعملي'])
            
            stats_msg = f"إجمالي المواد: {total_subjects}\n\n"
            stats_msg += f"مواد نظرية: {theoretical}\n"
            stats_msg += f"مواد عملية: {practical}\n"
            stats_msg += f"مواد نظرية وعملية: {mixed}\n"
                
            messagebox.showinfo("إحصائيات المواد", stats_msg)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات: {str(e)}")
        
    def export_subjects(self):
        """تصدير بيانات المواد"""
        messagebox.showinfo("تصدير", "ميزة التصدير ستتوفر قريباً!")

# للتوافق مع الأسماء القديمة
SubjectsTab = ModernSubjectsTab