"""
تبويب الطلاب الحديث - Modern Students Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from gui.modern_theme import theme
from gui.components import ModernCard, ModernForm, ModernTable, ModernButton, ActionButton
from utils.egyptian_id_parser import EgyptianIDParser

class ModernStudentsTab:
    def test_password_distribution(self):
        """اختبار شامل لتوزيع الأرقام السرية والتأكد من عدم التكرار"""
        # 1. جلب جميع الطلاب المستهدفين (مثلاً من قسم محدد أو الكل)
        filters = {}  # يمكن تخصيصها حسب الحاجة
        students = self.get_students_by_criteria(filters)
        if not students:
            print("لا يوجد طلاب للاختبار.")
            return False
        # 2. توزيع أرقام سرية افتراضية
        group_size = 10
        prefix = "TST"
        used_passwords = set(self.get_all_passwords())
        pw_counter = 1
        pw_map = {}
        for i, student in enumerate(students):
            group_num = (i // group_size) + 1
            pw_candidate = f"{prefix}{group_num:02d}{pw_counter:03d}"
            while pw_candidate in used_passwords:
                pw_counter += 1
                pw_candidate = f"{prefix}{group_num:02d}{pw_counter:03d}"
            pw_map[student['id']] = pw_candidate
            used_passwords.add(pw_candidate)
            pw_counter += 1
        # 3. تحديث قاعدة البيانات
        updated = 0
        for sid, pw in pw_map.items():
            try:
                self.db_manager.execute_update("UPDATE students SET password=? WHERE id=?", (pw, sid))
                updated += 1
            except Exception as e:
                print(f"خطأ في تحديث كلمة السر للطالب {sid}: {e}")
        print(f"تم تحديث الأرقام السرية لعدد {updated} طالب/ة.")
        # 4. التحقق من عدم وجود تكرار فعلي
        all_passwords = self.get_all_passwords()
        if len(all_passwords) != len(set(all_passwords)):
            print("يوجد تكرار في الأرقام السرية!")
            return False
        print("تم التوزيع بنجاح ولا يوجد تكرار في الأرقام السرية.")
        return True
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.setup_ui()
        self.refresh_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent, bg=theme.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رأس القسم
        self.setup_header(main_frame)
        
        # محتوى القسم
        content_frame = tk.Frame(main_frame, bg=theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=20)
        
        # تقسيم المحتوى إلى عمودين مع عرض ثابت للجانب الأيسر
        left_frame = tk.Frame(content_frame, bg=theme.colors['bg_primary'], width=350)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        left_frame.pack_propagate(True)  # السماح بالتمدد التلقائي ليظهر كل شيء
        
        right_frame = tk.Frame(content_frame, bg=theme.colors['bg_primary'])
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(15, 0))

        # النموذج والإجراءات (الجانب الأيسر) داخل إطار قابل للتمرير
        self.setup_scrollable_form_section(left_frame)

        # الجدول والبحث (الجانب الأيمن)
        self.setup_table_section(right_frame)

    def setup_header(self, parent):
        """إعداد رأس القسم"""
        header_frame = tk.Frame(parent, bg=theme.colors['bg_card'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # إطار داخلي للمحتوى
        content_frame = tk.Frame(header_frame, bg=theme.colors['bg_card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # العنوان والوصف
        title_frame = tk.Frame(content_frame, bg=theme.colors['bg_card'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        title_label = tk.Label(title_frame,
                              text="إدارة الطلاب",
                              font=theme.fonts['heading_1'],
                              bg=theme.colors['bg_card'],
                              fg=theme.colors['text_primary'])
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(title_frame,
                                text="إضافة وتعديل وإدارة بيانات الطلاب",
                                font=theme.fonts['body_medium'],
                                bg=theme.colors['bg_card'],
                                fg=theme.colors['text_secondary'])
        subtitle_label.pack(anchor=tk.W)
        
        # أزرار الإجراءات السريعة
        actions_frame = tk.Frame(content_frame, bg=theme.colors['bg_card'])
        actions_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار الإجراءات
        self.setup_quick_actions(actions_frame)
        
    def setup_quick_actions(self, parent):
        """إعداد الإجراءات السريعة"""
        # إطار الأزرار
        buttons_frame = tk.Frame(parent, bg=theme.colors['bg_card'])
        buttons_frame.pack(fill=tk.BOTH, expand=True)
        
        # أزرار الإجراءات السريعة
        actions = [
            {'icon': '📊', 'text': 'إحصائيات', 'command': self.show_statistics, 'color': 'info'},
            {'icon': '📤', 'text': 'تصدير', 'command': self.export_students, 'color': 'success'},
            {'icon': '📥', 'text': 'استيراد', 'command': self.import_students, 'color': 'warning'},
            {'icon': '🔄', 'text': 'تحديث', 'command': self.refresh_data, 'color': 'secondary'},
        ]
        
        for i, action in enumerate(actions):
            btn = ActionButton(buttons_frame, **action)
            btn.grid(row=0, column=i, padx=5, sticky='nsew')
            buttons_frame.grid_columnconfigure(i, weight=1)
            
    def setup_scrollable_form_section(self, parent):
        """إعداد قسم النموذج داخل إطار قابل للتمرير"""
        # إنشاء Canvas وشريط التمرير
        canvas = tk.Canvas(parent, bg=theme.colors['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        canvas.configure(yscrollcommand=scrollbar.set)

        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # إنشاء إطار داخلي داخل Canvas لوضع المحتوى القابل للتمرير
        scrollable_frame = ttk.Frame(canvas, style='Card.TFrame')

        # ربط الإطار القابل للتمرير بـ Canvas
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # تحديث منطقة التمرير عند تغيير حجم الإطار الداخلي
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

        # إعداد النموذج والأزرار داخل الإطار القابل للتمرير
        self.setup_form_section_content(scrollable_frame)

    def setup_form_section_content(self, parent):
        """إعداد محتوى قسم النموذج (النموذج والأزرار)"""
        # نموذج إضافة/تعديل الطالب
        self.form = ModernForm(parent, title="بيانات الطالب")
        self.form.pack(fill=tk.X, pady=(0, 20), padx=10)

        # إضافة الحقول المطلوبة (حذف الاسم الأخير والبريد الإلكتروني)
        self.form.add_field("الاسم الأول", "entry", width=25)
        self.form.add_field("الرقم القومي", "entry", width=25, callback=self.on_national_id_change)
        self.form.add_field("رقم الجلوس", "entry", width=25)
        self.form.add_field("الرقم السري", "entry", width=25)

        # حقول مستخرجة من الرقم القومي (للقراءة فقط)
        self.form.add_field("تاريخ الميلاد", "entry", width=25, state="readonly")
        self.form.add_field("النوع", "entry", width=25, state="readonly")
        self.form.add_field("المحافظة", "entry", width=25, state="readonly")
        self.form.add_field("السن", "entry", width=25, state="readonly")

        self.form.add_field("رقم الهاتف", "entry", width=25)

        # قائمة الأقسام (بدون رمز القسم أو وصف القسم)
        departments = self.get_departments_list()
        self.form.add_field("القسم", "combobox", options=departments, width=22)

        # أزرار العمليات
        self.setup_form_buttons(parent)

    def setup_form_buttons(self, parent):
        """إعداد أزرار النموذج"""
        # إطار الأزرار مباشرة بدون البطاقة لضمان الظهور
        buttons_frame = tk.Frame(parent, bg=theme.colors['bg_primary'], relief='raised', bd=1)
        buttons_frame.pack(fill=tk.X, pady=20)
        
        # عنوان القسم
        title_label = tk.Label(buttons_frame, 
                              text="العمليات", 
                              font=theme.fonts['heading_3'],
                              bg=theme.colors['bg_primary'], 
                              fg=theme.colors['text_primary'])
        title_label.pack(pady=(10, 5))
        
        # الأزرار في إطار داخلي
        inner_frame = tk.Frame(buttons_frame, bg=theme.colors['bg_primary'])
        inner_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # زر الإضافة
        self.add_btn = tk.Button(inner_frame, 
                                text="إضافة طالب جديد", 
                                command=self.add_student,
                                bg='#28a745',  # أخضر
                                fg='white',
                                font=('Arial', 12, 'bold'),
                                relief='raised',
                                bd=3,
                                padx=15,
                                pady=8,
                                cursor='hand2',
                                activebackground='#218838',
                                activeforeground='white')
        self.add_btn.pack(fill=tk.X, pady=3)
        
        # زر التعديل
        self.update_btn = tk.Button(inner_frame, 
                                   text="تعديل الطالب المختار", 
                                   command=self.update_student,
                                   bg='#ffc107',  # أصفر
                                   fg='black',
                                   font=('Arial', 12, 'bold'),
                                   relief='raised',
                                   bd=3,
                                   padx=15,
                                   pady=8,
                                   cursor='hand2',
                                   activebackground='#e0a800',
                                   activeforeground='black')
        self.update_btn.pack(fill=tk.X, pady=3)
        
        # زر الحذف
        self.delete_btn = tk.Button(inner_frame, 
                                   text="حذف الطالب المختار", 
                                   command=self.delete_student,
                                   bg='#dc3545',  # أحمر
                                   fg='white',
                                   font=('Arial', 12, 'bold'),
                                   relief='raised',
                                   bd=3,
                                   padx=15,
                                   pady=8,
                                   cursor='hand2',
                                   activebackground='#c82333',
                                   activeforeground='white')
        self.delete_btn.pack(fill=tk.X, pady=3)
        
        # زر المسح
        self.clear_btn = tk.Button(inner_frame, 
                                  text="مسح جميع الحقول", 
                                  command=self.clear_form,
                                  bg='#6c757d',  # رمادي
                                  fg='white',
                                  font=('Arial', 12, 'bold'),
                                  relief='raised',
                                  bd=3,
                                  padx=15,
                                  pady=8,
                                  cursor='hand2',
                                  activebackground='#5a6268',
                                  activeforeground='white')
        self.clear_btn.pack(fill=tk.X, pady=3)

        # زر تعيين/تعديل الأرقام السرية الجماعي
        self.set_passwords_btn = tk.Button(inner_frame,
            text="🔑 تعيين/تعديل الأرقام السرية للطلاب",
            command=self.open_set_passwords_window,
            bg='#2563eb', fg='white', font=('Arial', 12, 'bold'), relief='raised', bd=3,
            padx=15, pady=8, cursor='hand2', activebackground='#1e40af', activeforeground='white')
        self.set_passwords_btn.pack(fill=tk.X, pady=3)

    def open_set_passwords_window(self):
        """نافذة تفاعلية لتعيين الأرقام السرية لمجموعة طلاب (واجهة حديثة)"""
        win = tk.Toplevel(self.parent)
        win.title("تعيين/تعديل الأرقام السرية للطلاب")
        win.geometry("900x600") # تم تكبير النافذة
        win.transient(self.parent)
        win.grab_set()
        win.resizable(True, True) # السماح بتغيير حجم النافذة
        win.configure(bg=theme.colors['bg_card'])


        tk.Label(win, text="حدد الطلاب ثم أدخل الرقم السري الجديد أو استخدم التوزيع التلقائي", font=('Segoe UI', 14, 'bold'), bg=theme.colors['bg_card'], fg=theme.colors['primary']).pack(pady=15)
        # زر التوزيع التلقائي للأرقام السرية
        auto_pw_btn = tk.Button(win, text="⚡ توزيع تلقائي للأرقام السرية...", command=lambda: self.open_auto_password_distribution_window(win),
            bg='#059669', fg='white', font=('Segoe UI', 12, 'bold'), relief='raised', bd=2, padx=10, pady=5, cursor='hand2', activebackground='#047857', activeforeground='white')
        auto_pw_btn.pack(pady=(0, 10))

    def open_auto_password_distribution_window(self, parent_win):
        """نافذة توزيع الأرقام السرية حسب التخصص فقط"""
        win = tk.Toplevel(parent_win)
        win.title("توزيع تلقائي للأرقام السرية")
        win.geometry("800x600")
        win.transient(parent_win)
        win.grab_set()
        win.resizable(True, True)
        win.configure(bg=theme.colors['bg_card'])

        tk.Label(win, text="توزيع الأرقام السرية تلقائيًا حسب التخصص:", font=('Segoe UI', 15, 'bold'), bg=theme.colors['bg_card'], fg=theme.colors['primary']).pack(pady=15)

        # إطار المعايير
        filter_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        filter_frame.pack(fill=tk.X, padx=20, pady=5)

        # جلب القيم من قاعدة البيانات
        departments = self.get_departments_list()

        # متغيرات الاختيار
        dept_var = tk.StringVar()
        group_size_var = tk.IntVar(value=10)
        pw_prefix_var = tk.StringVar(value="STU")

        # القسم
        tk.Label(filter_frame, text="القسم:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=0, column=0, padx=5, pady=5, sticky='e')
        dept_cb = ttk.Combobox(filter_frame, textvariable=dept_var, values=departments, width=18, state="readonly")
        dept_cb.grid(row=0, column=1, padx=5, pady=5)

        # حجم المجموعة
        tk.Label(filter_frame, text="عدد الطلاب في كل مجموعة:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=1, column=0, padx=5, pady=5, sticky='e')
        group_size_entry = tk.Entry(filter_frame, textvariable=group_size_var, width=8, font=('Segoe UI', 12))
        group_size_entry.grid(row=1, column=1, padx=5, pady=5)

        # بادئة الرقم السري
        tk.Label(filter_frame, text="بادئة الرقم السري:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=1, column=2, padx=5, pady=5, sticky='e')
        pw_prefix_entry = tk.Entry(filter_frame, textvariable=pw_prefix_var, width=10, font=('Segoe UI', 12))
        pw_prefix_entry.grid(row=1, column=3, padx=5, pady=5)

        # زر تصفية الطلاب
        def update_students_table():
            tree.delete(*tree.get_children())
            filters = {}
            if dept_var.get():
                filters['department'] = dept_var.get()
            students = self.get_students_by_criteria(filters)
            for student in students:
                tree.insert('', tk.END, values=(
                    student['id'], student.get('first_name',''), student.get('national_id',''),
                    student.get('department_name',''),
                    student.get('password',''), ''
                ))

        filter_btn = tk.Button(filter_frame, text="تصفية الطلاب", command=update_students_table, bg='#2563eb', fg='white', font=('Segoe UI', 11, 'bold'), width=12)
        filter_btn.grid(row=1, column=4, padx=5, pady=5)

        # جدول الطلاب المستهدفين
        table_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        tree_scrollbar = ttk.Scrollbar(table_frame, orient="vertical")
        tree_scrollbar.pack(side="right", fill="y")
        columns = ("id", "الاسم الأول", "الرقم القومي", "القسم", "الرقم السري الحالي", "الرقم السري الجديد")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=16, selectmode='extended', yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.config(command=tree.yview)
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120 if col=="id" else 140)
        tree.pack(side="left", fill=tk.BOTH, expand=True)

        # زر توزيع الأرقام السرية
        def distribute_passwords():
            students = [tree.item(item)['values'] for item in tree.get_children()]
            if not students:
                messagebox.showwarning("تنبيه", "لا يوجد طلاب لتوزيع الأرقام السرية عليهم.")
                return
            prefix = pw_prefix_var.get() or "STU"
            for item in tree.get_children():
                vals = list(tree.item(item)['values'])
                vals[-1] = prefix  # الرقم السري كما أدخلته فقط
                tree.item(item, values=vals)

        # زر حفظ الأرقام السرية في قاعدة البيانات
        def save_passwords():
            updated = 0
            # جلب جميع الأرقام السرية الحالية لمنع التكرار
            used_passwords = set(self.get_all_passwords())
            duplicate_found = False
            for item in tree.get_children():
                vals = tree.item(item)['values']
                sid = vals[0]
                new_pw = vals[-1]
                if new_pw:
                    if new_pw in used_passwords:
                        duplicate_found = True
                        tree.selection_set(item)
                        continue
                    try:
                        self.db_manager.execute_update("UPDATE students SET password=? WHERE id=?", (new_pw, sid))
                        used_passwords.add(new_pw)
                        updated += 1
                    except Exception as e:
                        print(f"خطأ في تحديث كلمة السر للطالب {sid}: {e}")
            if duplicate_found:
                messagebox.showerror("تكرار الأرقام السرية", "هناك أرقام سرية مكررة! يرجى التأكد من أن كل رقم سري فريد وعدم تكراره.")
            else:
                messagebox.showinfo("تم الحفظ", f"تم تحديث الأرقام السرية لعدد {updated} طالب/ة.")
            update_students_table()

        # أزرار العمليات
        ops_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        ops_frame.pack(pady=10)
        tk.Button(ops_frame, text="توزيع الأرقام السرية", command=distribute_passwords, bg='#059669', fg='white', font=('Segoe UI', 12, 'bold'), width=18).pack(side=tk.LEFT, padx=8)
        tk.Button(ops_frame, text="حفظ الأرقام السرية", command=save_passwords, bg='#2563eb', fg='white', font=('Segoe UI', 12, 'bold'), width=16).pack(side=tk.LEFT, padx=8)
        tk.Button(ops_frame, text="إغلاق", command=win.destroy, bg='#64748b', fg='white', font=('Segoe UI', 11), width=10).pack(side=tk.LEFT, padx=8)

        # تحميل الطلاب مبدئيًا
        update_students_table()

    def get_unique_column_values(self, table, column):
        """جلب القيم الفريدة لعمود معين من جدول"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            cursor = self.db_manager.connection.cursor()
            cursor.execute(f"SELECT DISTINCT {column} FROM {table} WHERE {column} IS NOT NULL AND {column} != ''")
            rows = cursor.fetchall()
            return [row[0] for row in rows if row[0]]
        except Exception as e:
            print(f"خطأ في جلب القيم الفريدة للعمود {column}: {e}")
            return []

    def get_students_by_criteria(self, filters):
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            cursor = self.db_manager.connection.cursor()
            query = """
                SELECT s.id, s.first_name, s.national_id, s.password, d.name as department_name
                FROM students s
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE 1=1
            """
            params = []
            if 'department' in filters and filters['department']:
                query += " AND s.department_id = (SELECT id FROM departments WHERE name = ?)"
                params.append(filters['department'])
            query += " ORDER BY s.id"
            cursor.execute(query, params)
            rows = cursor.fetchall()
            students = []
            for row in rows:
                students.append({
                    'id': row[0],
                    'first_name': row[1],
                    'national_id': row[2],
                    'password': row[3],
                    'department_name': row[4]
                })
            return students
        except Exception as e:
            print(f"خطأ في جلب الطلاب حسب المعايير: {e}")
            return []

    def get_all_passwords(self):
        """جلب جميع الأرقام السرية الحالية للطلاب"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT password FROM students WHERE password IS NOT NULL AND password != ''")
            rows = cursor.fetchall()
            return [row[0] for row in rows if row[0]]
        except Exception as e:
            print(f"خطأ في جلب جميع الأرقام السرية: {e}")
            return []

        # جدول الطلاب مع تحديد متعدد داخل إطار قابل للتمرير
        table_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        tree_scrollbar = ttk.Scrollbar(table_frame, orient="vertical")
        tree_scrollbar.pack(side="right", fill="y")

        columns = ("id", "الاسم الأول", "الرقم القومي", "القسم", "الحالة", "الرقم السري الحالي")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=16, selectmode='extended', yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.config(command=tree.yview)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120 if col=="id" else 150) # تم زيادة عرض الأعمدة قليلاً
        tree.pack(side="left", fill=tk.BOTH, expand=True)

        # تحميل الطلاب
        students = self.db_manager.get_students()
        for student in students:
            tree.insert('', tk.END, values=(
                student['id'],
                student.get('first_name', ''),
                student.get('national_id', ''),
                student.get('department_name', ''),
                student.get('status', ''),
                student.get('password', '')
            ))

        # إدخال الرقم السري الجديد
        pw_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        pw_frame.pack(pady=10)
        tk.Label(pw_frame, text="الرقم السري الجديد:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).pack(side=tk.LEFT, padx=5)
        password_var = tk.StringVar()
        pw_entry = tk.Entry(pw_frame, textvariable=password_var, font=('Segoe UI', 13), width=22)
        pw_entry.pack(side=tk.LEFT, padx=5)

        def set_passwords():
            selected = tree.selection()
            new_pw = password_var.get().strip()
            if not selected:
                messagebox.showwarning("تنبيه", "يرجى تحديد طالب واحد على الأقل.")
                return
            if not new_pw:
                messagebox.showwarning("تنبيه", "يرجى إدخال الرقم السري الجديد.")
                return
            ids = [tree.item(item)['values'][0] for item in selected]
            try:
                for sid in ids:
                    self.db_manager.execute_update("UPDATE students SET password=? WHERE id=?", (new_pw, sid))
                messagebox.showinfo("نجاح", f"تم تعيين الرقم السري الجديد لعدد {len(ids)} طالب/ة.")
                # تحديث الجدول
                for item in selected:
                    vals = list(tree.item(item)['values'])
                    vals[-1] = new_pw
                    tree.item(item, values=vals)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تعيين الرقم السري: {e}")

        btn = tk.Button(win, text="تعيين الرقم السري", command=set_passwords, bg='#2563eb', fg='white', font=('Segoe UI', 13, 'bold'), width=18)
        btn.pack(pady=10)

        tk.Button(win, text="إغلاق", command=win.destroy, bg='#64748b', fg='white', font=('Segoe UI', 11), width=10).pack(pady=5)
        
    def setup_table_section(self, parent):
        """إعداد قسم الجدول"""
        # بطاقة البحث والتصفية
        search_card = ModernCard(parent, title="البحث والتصفية")
        search_card.pack(fill=tk.X, pady=(0, 20))
        
        # شريط البحث
        search_frame = tk.Frame(search_card.content_frame, bg=theme.colors['bg_card'])
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل البحث
        search_label = tk.Label(search_frame,
                               text="البحث:",
                               font=theme.fonts['body_medium_bold'],
                               bg=theme.colors['bg_card'],
                               fg=theme.colors['text_primary'])
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=theme.fonts['body_medium'],
                               bg=theme.colors['bg_card'],
                               fg=theme.colors['text_primary'],
                               relief='solid',
                               bd=1,
                               width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_students)
        
        # زر البحث
        ModernButton(search_frame, "بحث", self.search_students, 'primary', '🔍', 'small').pack(side=tk.LEFT)
        
        # بطاقة الجدول
        table_card = ModernCard(parent, title="قائمة الطلاب")
        table_card.pack(fill=tk.BOTH, expand=True)
        
        # الجدول مع الأعمدة المحدثة (بدون البريد الإلكتروني)
        columns = ['ID', 'الاسم الأول', 'الرقم القومي', 'رقم الجلوس', 'تاريخ الميلاد', 'النوع', 'السن', 'المحافظة', 'القسم', 'الهاتف']
        # تم حذف أي أعمدة تخص رمز القسم أو وصف القسم
        self.table = ModernTable(table_card.content_frame, columns, height=15)
        self.table.pack(fill=tk.BOTH, expand=True)
        
        # ربط حدث التحديد
        self.table.tree.bind('<<TreeviewSelect>>', self.on_student_select)
        
    def on_national_id_change(self, event=None):
        """معالج تغيير الرقم القومي لاستخراج البيانات تلقائياً"""
        try:
            national_id = self.form.get_value("الرقم القومي")
            if not national_id or len(national_id) != 14:
                # مسح الحقول المستخرجة إذا كان الرقم غير صحيح
                self.form.set_value("تاريخ الميلاد", "")
                self.form.set_value("النوع", "")
                self.form.set_value("المحافظة", "")
                self.form.set_value("السن", "")
                return
            
            # استخراج البيانات من الرقم القومي المصري
            parsed_data = EgyptianIDParser.parse_egyptian_id(national_id)
            
            if parsed_data['valid']:
                # تحديث الحقول المستخرجة
                if parsed_data['birth_date']:
                    self.form.set_value("تاريخ الميلاد", parsed_data['birth_date'].strftime('%Y-%m-%d'))
                
                if parsed_data['gender']:
                    self.form.set_value("النوع", parsed_data['gender'])
                
                if parsed_data['governorate']:
                    self.form.set_value("المحافظة", parsed_data['governorate'])
                
                if parsed_data['age'] is not None:
                    self.form.set_value("السن", parsed_data['age'])
            else:
                # مسح الحقول إذا كان الرقم غير صحيح
                self.form.set_value("تاريخ الميلاد", "")
                self.form.set_value("النوع", "")
                self.form.set_value("المحافظة", "")
                self.form.set_value("السن", "")
                
        except Exception as e:
            print(f"خطأ في معالجة الرقم القومي: {e}")

    def get_departments_list(self):
        """الحصول على قائمة الأقسام (بدون رمز القسم أو الوصف)"""
        try:
            departments = self.db_manager.get_departments()
            # فقط اسم القسم
            return [dept['name'] for dept in departments]
        except:
            return []
            
    def add_student(self):
        """إضافة طالب جديد مع التحقق المحسن"""
        try:
            if not self.db_manager or not hasattr(self.db_manager, 'connection'):
                messagebox.showerror("خطأ في الاتصال", "قاعدة البيانات غير متصلة!")
                return
            values = self.form.get_values()
            name = values.get('الاسم الأول', '').strip()
            national_id = values.get('الرقم القومي', '').strip()
            seat_number = values.get('رقم الجلوس', '').strip()
            password = values.get('الرقم السري', '').strip()
            phone = values.get('رقم الهاتف', '').strip()
            address = values.get('العنوان', '').strip()
            department = values.get('القسم', '').strip()
            # التحقق من صحة البيانات
            if not name:
                messagebox.showerror("خطأ في الإدخال", "الاسم الأول مطلوب!")
                return
            if not national_id:
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي مطلوب!")
                return
            if len(name) < 2:
                messagebox.showerror("خطأ في الإدخال", "الاسم الأول يجب أن يكون على الأقل حرفين.")
                return
            if not national_id.isdigit() or len(national_id) != 14:
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي يجب أن يكون 14 رقمًا.")
                return
            # التحقق من صحة الرقم القومي
            if not EgyptianIDParser.is_valid_egyptian_id(national_id):
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي غير صحيح! يجب أن يكون 14 رقمًا.")
                return
            parsed_data = EgyptianIDParser.parse_egyptian_id(national_id)
            department_id = self.get_department_id(department)
            student_data = {
                'first_name': name,
                'national_id': national_id,
                'seat_number': seat_number,
                'password': password,
                'birth_date': parsed_data.get('birth_date').strftime('%Y-%m-%d') if parsed_data.get('birth_date') else '',
                'gender': parsed_data.get('gender', ''),
                'governorate': parsed_data.get('governorate', ''),
                'age': parsed_data.get('age', ''),
                'phone': phone,
                'department_id': department_id,
                'address': address
            }
            try:
                student_id = self.save_student_to_db(student_data)
                success_message = f"""تم حفظ الطالب بنجاح!\n\nالاسم: {name}\nالرقم القومي: {national_id}\nرقم الجلوس: {seat_number}\nتاريخ الميلاد: {parsed_data.get('birth_date', '')}\nالنوع: {parsed_data.get('gender', '')}\nالمحافظة: {parsed_data.get('governorate', '')}\nالسن: {parsed_data.get('age', '')}\nمعرف الطالب في النظام: {student_id}"""
                messagebox.showinfo("تم الحفظ بنجاح", success_message)
                self.refresh_data()
                self.clear_form()
            except Exception as db_error:
                messagebox.showerror("خطأ في الحفظ", f"فشل في حفظ البيانات: {str(db_error)}")
        except Exception as e:
            messagebox.showerror("خطأ غير متوقع", f"فشل في معالجة بيانات الطالب: {str(e)}")
    
    def save_student_to_db(self, student_data):
        """حفظ بيانات الطالب في قاعدة البيانات"""
        try:
            # التأكد من أن قاعدة البيانات متصلة
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            # استخدام الدالة الجديدة المحدثة
            if hasattr(self.db_manager, 'add_student_new'):
                student_id = self.db_manager.add_student_new(student_data)
                return student_id
            else:
                # الطريقة البديلة
                query = """
                    INSERT INTO students (
                        first_name, national_id, seat_number, password, 
                        birth_date, gender, governorate, age, phone, 
                        department_id, address, enrollment_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, date('now'), 'نشط')
                """
                
                params = (
                    student_data['first_name'],
                    student_data['national_id'],
                    student_data['seat_number'],
                    student_data['password'],
                    student_data['birth_date'],
                    student_data['gender'],
                    student_data['governorate'],
                    student_data['age'],
                    student_data['phone'],
                    student_data['department_id'],
                    student_data['address']
                )
                
                cursor = self.db_manager.connection.cursor()
                cursor.execute(query, params)
                self.db_manager.connection.commit()
                student_id = cursor.lastrowid
                return student_id
                
        except Exception as e:
            raise Exception(f"خطأ في قاعدة البيانات: {str(e)}")
    
    def get_student_data(self, student_id):
        """الحصول على بيانات الطالب من قاعدة البيانات"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            query = """
                SELECT first_name, national_id, seat_number, password, birth_date,
                       gender, governorate, age, phone, department_id, address
                FROM students WHERE id = ?
            """
            
            cursor = self.db_manager.connection.cursor()
            cursor.execute(query, (student_id,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'first_name': row[0],
                    'national_id': row[1],
                    'seat_number': row[2],
                    'password': row[3],
                    'birth_date': row[4],
                    'gender': row[5],
                    'governorate': row[6],
                    'age': row[7],
                    'phone': row[8],
                    'department_id': row[9],
                    'address': row[10]
                }
            return None
            
        except Exception as e:
            raise Exception(f"خطأ في استرجاع بيانات الطالب: {str(e)}")

    def update_student_in_db(self, student_data):
        """تحديث بيانات الطالب في قاعدة البيانات"""
        try:
            # التأكد من أن قاعدة البيانات متصلة
            if not self.db_manager.connection:
                self.db_manager.connect()

            # التحقق من الأعمدة الموجودة في الجدول
            cursor = self.db_manager.connection.cursor()
            cursor.execute("PRAGMA table_info(students)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"الأعمدة الموجودة في جدول الطلاب: {columns}")

            # بناء استعلام التحديث بناءً على الأعمدة الموجودة
            update_fields = []
            params = []

            # الحقول الأساسية
            if 'first_name' in columns:
                update_fields.append("first_name = ?")
                params.append(student_data['first_name'])

            if 'national_id' in columns:
                update_fields.append("national_id = ?")
                params.append(student_data['national_id'])

            if 'seat_number' in columns:
                update_fields.append("seat_number = ?")
                params.append(student_data['seat_number'])

            if 'password' in columns:
                update_fields.append("password = ?")
                params.append(student_data['password'])

            if 'birth_date' in columns:
                update_fields.append("birth_date = ?")
                params.append(student_data['birth_date'])
            elif 'date_of_birth' in columns:
                update_fields.append("date_of_birth = ?")
                params.append(student_data['birth_date'])

            if 'gender' in columns:
                update_fields.append("gender = ?")
                params.append(student_data['gender'])

            if 'governorate' in columns:
                update_fields.append("governorate = ?")
                params.append(student_data['governorate'])

            if 'age' in columns:
                update_fields.append("age = ?")
                params.append(student_data['age'])

            if 'phone' in columns:
                update_fields.append("phone = ?")
                params.append(student_data['phone'])

            if 'department_id' in columns:
                update_fields.append("department_id = ?")
                params.append(student_data['department_id'])

            if 'address' in columns:
                update_fields.append("address = ?")
                params.append(student_data['address'])

            # إضافة معرف الطالب في النهاية
            params.append(student_data['id'])

            if not update_fields:
                raise Exception("لا توجد حقول صالحة للتحديث")

            # تنفيذ الاستعلام
            query = f"UPDATE students SET {', '.join(update_fields)} WHERE id = ?"
            print(f"استعلام التحديث: {query}")
            print(f"المعاملات: {params}")

            cursor.execute(query, params)
            affected_rows = cursor.rowcount
            self.db_manager.connection.commit()

            print(f"تم تحديث {affected_rows} صف")

            if affected_rows == 0:
                raise Exception("لم يتم العثور على الطالب للتحديث")

        except Exception as e:
            print(f"خطأ في تحديث قاعدة البيانات: {str(e)}")
            raise Exception(f"خطأ في تحديث قاعدة البيانات: {str(e)}")
            
    def delete_student_from_db(self, student_id):
        """حذف الطالب من قاعدة البيانات"""
        try:
            # التأكد من أن قاعدة البيانات متصلة
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            # استعلام الحذف
            query = "DELETE FROM students WHERE id = ?"
            
            cursor = self.db_manager.connection.cursor()
            cursor.execute(query, (student_id,))
            self.db_manager.connection.commit()
                
        except Exception as e:
            raise Exception(f"خطأ في حذف الطالب من قاعدة البيانات: {str(e)}")
            
    def validate_student_data(self, name, national_id, seat_number, phone, department, password):
        """التحقق من صحة بيانات الطالب"""
        if not password:
            messagebox.showerror("خطأ في الإدخال", "الرقم السري مطلوب!")
            return False

        if not name:
            messagebox.showerror("خطأ في الإدخال", "الاسم الأول مطلوب!")
            return False

        if len(name) < 2:
            messagebox.showerror("خطأ في الإدخال", "الاسم الأول يجب أن يكون على الأقل حرفين.")
            return False

        if not national_id:
            messagebox.showerror("خطأ في الإدخال", "الرقم القومي مطلوب!")
            return False

        if not national_id.isdigit() or len(national_id) != 14:
            messagebox.showerror("خطأ في الإدخال", "الرقم القومي يجب أن يكون 14 رقمًا.")
            return False

        if not EgyptianIDParser.is_valid_egyptian_id(national_id):
            messagebox.showerror("خطأ في الإدخال", "الرقم القومي غير صحيح!")
            return False

        if not seat_number:
            messagebox.showerror("خطأ في الإدخال", "رقم الجلوس مطلوب!")
            return False

        if phone and (not phone.isdigit() or len(phone) != 11 or not phone.startswith(('010', '011', '012', '015'))):
            messagebox.showerror("خطأ في الإدخال", "رقم الهاتف يجب أن يكون 11 رقمًا ويبدأ بـ 010 أو 011 أو 012 أو 015")
            return False

        if not department:
            messagebox.showerror("خطأ في الإدخال", "يجب اختيار القسم!")
            return False

        return True

    def update_student(self):
        """تعديل الطالب المحدد مع التحقق المحسن"""
        selected = self.table.tree.selection()
        if not selected:
            messagebox.showerror("خطأ في التحديد", "يرجى تحديد طالب للتعديل!")
            return

        try:
            item = self.table.tree.item(selected[0])
            student_id = item['values'][0]
            values = self.form.get_values()
            
            # استخراج البيانات
            name = values.get('الاسم الأول', '').strip()
            national_id = values.get('الرقم القومي', '').strip()
            seat_number = values.get('رقم الجلوس', '').strip()
            phone = values.get('رقم الهاتف', '').strip()
            address = values.get('العنوان', '').strip()
            department = values.get('القسم', '').strip()
            password = values.get('الرقم السري', '').strip()

            # التحقق من صحة البيانات
            if not self.validate_student_data(name, national_id, seat_number, phone, department, password):
                return

            # التحقق من تكرار الرقم القومي
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT id FROM students WHERE national_id = ? AND id != ?", (national_id, student_id))
            if cursor.fetchone():
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي مستخدم بالفعل لطالب آخر!")
                return
            try:
                parsed_data = EgyptianIDParser.parse_egyptian_id(national_id)
                department_id = self.get_department_id(department)
                if not department_id:
                    messagebox.showerror("خطأ في الإدخال", "القسم غير موجود في قاعدة البيانات!")
                    return

                # تحديث البيانات
                student_data = {
                    'id': student_id,
                    'first_name': name,
                    'national_id': national_id,
                    'seat_number': seat_number,
                    'password': password,
                    'birth_date': parsed_data.get('birth_date').strftime('%Y-%m-%d') if parsed_data.get('birth_date') else '',
                    'gender': parsed_data.get('gender', ''),
                    'governorate': parsed_data.get('governorate', ''),
                    'age': parsed_data.get('age', ''),
                    'phone': phone,
                    'department_id': department_id,
                    'address': address
                }

                # حفظ البيانات القديمة للسجل
                old_data = self.get_student_data(student_id)
                
                # تحديث البيانات في قاعدة البيانات
                self.update_student_in_db(student_data)

                # إعداد رسالة النجاح
                success_message = "تم تحديث بيانات الطالب بنجاح:\n\n"
                success_message += f"الاسم: {name}\n"
                success_message += f"الرقم القومي: {national_id}\n"
                success_message += f"رقم الجلوس: {seat_number}\n"
                success_message += f"القسم: {department}\n"
                success_message += f"رقم الهاتف: {phone}\n"
                success_message += f"العنوان: {address if address else 'غير محدد'}\n"
                success_message += f"\nتم التحديث في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                # عرض رسالة النجاح
                messagebox.showinfo("نجح", success_message)

                # تحديث الواجهة
                self.clear_form()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خطأ في تحديث البيانات", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")
                return
        except Exception as e:
            messagebox.showerror("خطأ غير متوقع", f"فشل في تعديل الطالب: {str(e)}")
            
    def delete_student(self):
        """حذف الطالب المحدد"""
        selected = self.table.tree.selection()
        if not selected:
            messagebox.showerror("خطأ", "يرجى تحديد طالب للحذف!")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الطالب؟"):
            try:
                # الحصول على معرف الطالب المحدد
                item = self.table.tree.item(selected[0])
                student_id = item['values'][0]
                
                # حذف الطالب من قاعدة البيانات
                self.delete_student_from_db(student_id)
                messagebox.showinfo("نجح", "تم حذف الطالب بنجاح!")
                self.refresh_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الطالب: {str(e)}")
                
    def clear_form(self):
        """مسح النموذج"""
        self.form.clear()
        
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # مسح الجدول
            self.table.clear()
            
            # تحميل الطلاب من قاعدة البيانات
            students = self.get_students_from_db()
            
            for student in students:
                # الحصول على اسم القسم
                department_name = self.get_department_name(student.get('department_id'))
                
                self.table.insert_row([
                    student.get('id', ''),
                    student.get('first_name', ''),
                    student.get('national_id', ''),
                    student.get('seat_number', ''),
                    student.get('birth_date', ''),
                    student.get('gender', ''),
                    student.get('age', ''),
                    student.get('governorate', ''),
                    department_name,
                    student.get('phone', '')
                ])
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def get_students_from_db(self):
        """جلب الطلاب من قاعدة البيانات"""
        try:
            # التأكد من الاتصال
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.connection.cursor()
            
            # التحقق من وجود الأعمدة الجديدة في جدول students
            cursor.execute("PRAGMA table_info(students)")
            columns_info = cursor.fetchall()
            columns = [column[1] for column in columns_info]
            
            # إنشاء استعلام حسب الأعمدة المتاحة
            if 'national_id' in columns and 'seat_number' in columns:
                query = """
                    SELECT id, first_name, national_id, seat_number, birth_date, 
                           gender, governorate, age, phone, department_id, address
                    FROM students 
                    ORDER BY id DESC
                """
            else:
                # استخدام الهيكل القديم إذا لم تكن الأعمدة الجديدة موجودة
                query = """
                    SELECT id, 
                           COALESCE(first_name, '') as first_name,
                           COALESCE(student_id, '') as national_id, 
                           '' as seat_number, 
                           COALESCE(date_of_birth, '') as birth_date,
                           COALESCE(gender, '') as gender, 
                           '' as governorate, 
                           '' as age, 
                           COALESCE(phone, '') as phone, 
                           COALESCE(section_id, department_id, '') as department_id, 
                           COALESCE(address, '') as address
                    FROM students 
                    ORDER BY id DESC
                """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            print(f"تم جلب {len(rows)} طالب من قاعدة البيانات")  # للتشخيص
            
            # تحويل النتائج إلى قاموس
            students = []
            for row in rows:
                student = {
                    'id': row[0],
                    'first_name': row[1],
                    'national_id': row[2],
                    'seat_number': row[3],
                    'birth_date': row[4],
                    'gender': row[5],
                    'governorate': row[6],
                    'age': row[7],
                    'phone': row[8],
                    'department_id': row[9],
                    'address': row[10] if len(row) > 10 else ''
                }
                students.append(student)
                print(f"طالب: {student}")  # للتشخيص
            
            return students
                
        except Exception as e:
            print(f"خطأ في جلب البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
            
    def filter_students(self, event=None):
        """تصفية الطلاب"""
        search_term = self.search_var.get().lower()
        
        # TODO: تنفيذ التصفية
        pass
        
    def search_students(self):
        """البحث عن الطلاب"""
        search_term = self.search_var.get()
        if not search_term:
            self.refresh_data()
            return
            
        # TODO: تنفيذ البحث
        pass
        
    def on_student_select(self, event):
        """عند تحديد طالب من الجدول"""
        selected = self.table.tree.selection()
        if not selected:
            return
            
        try:
            # الحصول على بيانات الطالب المحدد
            item = self.table.tree.item(selected[0])
            student_id = item['values'][0]
            
            # تحميل بيانات الطالب في النموذج
            # TODO: تحميل البيانات في النموذج
            pass
            
        except Exception as e:
            print(f"خطأ في تحديد الطالب: {e}")
            
    def get_department_id(self, department_name):
        """الحصول على معرف القسم (بدون رمز القسم أو الوصف)"""
        try:
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept['name'] == department_name:
                    return dept['id']
            return None
        except:
            return None
            
    def get_department_name(self, department_id):
        """الحصول على اسم القسم (بدون رمز القسم أو الوصف)"""
        try:
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept['id'] == department_id:
                    return dept['name']
            return 'غير محدد'
        except:
            return 'غير محدد'
            
    def show_statistics(self):
        """عرض إحصائيات الطلاب"""
        messagebox.showinfo("إحصائيات", "ميزة الإحصائيات ستتوفر قريباً!")
        
    def export_students(self):
        """تصدير بيانات الطلاب"""
        messagebox.showinfo("تصدير", "ميزة التصدير ستتوفر قريباً!")
        
    def import_students(self):
        """استيراد بيانات الطلاب"""
        messagebox.showinfo("استيراد", "ميزة الاستيراد ستتوفر قريباً!")
        
    def on_student_select(self, event):
        """معالج تحديد الطالب من الجدول"""
        try:
            selected = self.table.tree.selection()
            if selected:
                # الحصول على بيانات الطالب المحدد
                item = self.table.tree.item(selected[0])
                values = item['values']
                
                # تعبئة النموذج ببيانات الطالب
                if len(values) >= 10:
                    self.form.set_value("الاسم الأول", values[1] if values[1] else '')
                    self.form.set_value("الرقم القومي", values[2] if values[2] else '')
                    self.form.set_value("رقم الجلوس", values[3] if values[3] else '')
                    self.form.set_value("تاريخ الميلاد", values[4] if values[4] else '')
                    self.form.set_value("النوع", values[5] if values[5] else '')
                    self.form.set_value("السن", values[6] if values[6] else '')
                    self.form.set_value("المحافظة", values[7] if values[7] else '')
                    self.form.set_value("رقم الهاتف", values[9] if values[9] else '')
                    
                    # البحث عن القسم
                    department_name = values[8] if values[8] else ''
                    if department_name:
                        self.form.set_value("القسم", department_name)
                    
                    # جلب بيانات إضافية من قاعدة البيانات
                    student_id = values[0]
                    additional_data = self.get_additional_student_data(student_id)
                    if additional_data:
                        self.form.set_value("الرقم السري", additional_data.get('password', ''))
                        self.form.set_value("العنوان", additional_data.get('address', ''))
                        
        except Exception as e:
            print(f"خطأ في تحديد الطالب: {e}")
            
    def get_additional_student_data(self, student_id):
        """جلب بيانات إضافية للطالب"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
                
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT password, address FROM students WHERE id = ?", (student_id,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'password': result[0] if result[0] else '',
                    'address': result[1] if result[1] else ''
                }
            return {}
            
        except Exception as e:
            print(f"خطأ في جلب البيانات الإضافية: {e}")
            return {}
    
    def search_students(self):
        """البحث في الطلاب"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.refresh_data()
            return
            
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
                
            cursor = self.db_manager.connection.cursor()
            
            # البحث في عدة حقول
            query = """
                SELECT id, first_name, national_id, seat_number, birth_date, 
                       gender, governorate, age, phone, department_id, address
                FROM students 
                WHERE first_name LIKE ? OR national_id LIKE ? OR seat_number LIKE ? OR phone LIKE ?
                ORDER BY id DESC
            """
            
            search_pattern = f"%{search_term}%"
            cursor.execute(query, (search_pattern, search_pattern, search_pattern, search_pattern))
            students = cursor.fetchall()
            
            # مسح الجدول الحالي
            self.table.clear()
            
            # إضافة النتائج
            for student in students:
                department_name = self.get_department_name(student[9]) if student[9] else ''
                self.table.insert_row([
                    student[0],  # ID
                    student[1],  # first_name
                    student[2],  # national_id
                    student[3],  # seat_number
                    student[4],  # birth_date
                    student[5],  # gender
                    student[7],  # age
                    student[6],  # governorate
                    department_name,  # department_name
                    student[8]   # phone
                ])
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def filter_students(self, event=None):
        """تصفية الطلاب أثناء الكتابة"""
        # استدعاء البحث مع تأخير صغير
        self.parent.after(300, self.search_students)

    def distribute_passwords(self, students, password_value):
        """توزيع الرقم السري كما هو على جميع الطلاب المحددين"""
        for student in students:
            self.db_manager.execute_update(
                "UPDATE students SET password=? WHERE id=?",
                (password_value, student['id'])
            )

    def open_auto_password_distribution_window(self, parent_win):
        """نافذة توزيع الأرقام السرية حسب التخصص فقط"""
        win = tk.Toplevel(parent_win)
        win.title("توزيع تلقائي للأرقام السرية")
        win.geometry("800x600")
        win.transient(parent_win)
        win.grab_set()
        win.resizable(True, True)
        win.configure(bg=theme.colors['bg_card'])

        tk.Label(win, text="توزيع الأرقام السرية تلقائيًا حسب التخصص:", font=('Segoe UI', 15, 'bold'), bg=theme.colors['bg_card'], fg=theme.colors['primary']).pack(pady=15)

        # إطار المعايير
        filter_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        filter_frame.pack(fill=tk.X, padx=20, pady=5)

        # جلب القيم من قاعدة البيانات
        departments = self.get_departments_list()

        # متغيرات الاختيار
        dept_var = tk.StringVar()
        group_size_var = tk.IntVar(value=10)
        pw_prefix_var = tk.StringVar(value="STU")

        # القسم
        tk.Label(filter_frame, text="القسم:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=0, column=0, padx=5, pady=5, sticky='e')
        dept_cb = ttk.Combobox(filter_frame, textvariable=dept_var, values=departments, width=18, state="readonly")
        dept_cb.grid(row=0, column=1, padx=5, pady=5)

        # حجم المجموعة
        tk.Label(filter_frame, text="عدد الطلاب في كل مجموعة:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=1, column=0, padx=5, pady=5, sticky='e')
        group_size_entry = tk.Entry(filter_frame, textvariable=group_size_var, width=8, font=('Segoe UI', 12))
        group_size_entry.grid(row=1, column=1, padx=5, pady=5)

        # بادئة الرقم السري
        tk.Label(filter_frame, text="بادئة الرقم السري:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=1, column=2, padx=5, pady=5, sticky='e')
        pw_prefix_entry = tk.Entry(filter_frame, textvariable=pw_prefix_var, width=10, font=('Segoe UI', 12))
        pw_prefix_entry.grid(row=1, column=3, padx=5, pady=5)

        # زر تصفية الطلاب
        def update_students_table():
            tree.delete(*tree.get_children())
            filters = {}
            if dept_var.get():
                filters['department'] = dept_var.get()
            students = self.get_students_by_criteria(filters)
            for student in students:
                tree.insert('', tk.END, values=(
                    student['id'], student.get('first_name',''), student.get('national_id',''),
                    student.get('department_name',''),
                    student.get('password',''), ''
                ))

        filter_btn = tk.Button(filter_frame, text="تصفية الطلاب", command=update_students_table, bg='#2563eb', fg='white', font=('Segoe UI', 11, 'bold'), width=12)
        filter_btn.grid(row=1, column=4, padx=5, pady=5)

        # جدول الطلاب المستهدفين
        table_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        tree_scrollbar = ttk.Scrollbar(table_frame, orient="vertical")
        tree_scrollbar.pack(side="right", fill="y")
        columns = ("id", "الاسم الأول", "الرقم القومي", "القسم", "الرقم السري الحالي", "الرقم السري الجديد")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=16, selectmode='extended', yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.config(command=tree.yview)
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120 if col=="id" else 140)
        tree.pack(side="left", fill=tk.BOTH, expand=True)

        # زر توزيع الأرقام السرية
        def distribute_passwords():
            students = [tree.item(item)['values'] for item in tree.get_children()]
            if not students:
                messagebox.showwarning("تنبيه", "لا يوجد طلاب لتوزيع الأرقام السرية عليهم.")
                return
            prefix = pw_prefix_var.get() or "STU"
            for item in tree.get_children():
                vals = list(tree.item(item)['values'])
                vals[-1] = prefix  # الرقم السري كما أدخلته فقط
                tree.item(item, values=vals)

        # زر حفظ الأرقام السرية في قاعدة البيانات
        def save_passwords():
            updated = 0
            for item in tree.get_children():
                vals = tree.item(item)['values']
                sid = vals[0]
                new_pw = vals[-1]
                if new_pw:
                    try:
                        self.db_manager.execute_update("UPDATE students SET password=? WHERE id=?", (new_pw, sid))
                        updated += 1
                    except Exception as e:
                        print(f"خطأ في تحديث كلمة السر للطالب {sid}: {e}")
            messagebox.showinfo("تم الحفظ", f"تم تحديث الأرقام السرية لعدد {updated} طالب/ة.")
            update_students_table()

        # أزرار العمليات
        ops_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        ops_frame.pack(pady=10)
        tk.Button(ops_frame, text="توزيع الأرقام السرية", command=distribute_passwords, bg='#059669', fg='white', font=('Segoe UI', 12, 'bold'), width=18).pack(side=tk.LEFT, padx=8)
        tk.Button(ops_frame, text="حفظ الأرقام السرية", command=save_passwords, bg='#2563eb', fg='white', font=('Segoe UI', 12, 'bold'), width=16).pack(side=tk.LEFT, padx=8)
        tk.Button(ops_frame, text="إغلاق", command=win.destroy, bg='#64748b', fg='white', font=('Segoe UI', 11), width=10).pack(side=tk.LEFT, padx=8)

        # تحميل الطلاب مبدئيًا
        update_students_table()

    def get_unique_column_values(self, table, column):
        """جلب القيم الفريدة لعمود معين من جدول"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            cursor = self.db_manager.connection.cursor()
            cursor.execute(f"SELECT DISTINCT {column} FROM {table} WHERE {column} IS NOT NULL AND {column} != ''")
            rows = cursor.fetchall()
            return [row[0] for row in rows if row[0]]
        except Exception as e:
            print(f"خطأ في جلب القيم الفريدة للعمود {column}: {e}")
            return []

    def get_students_by_criteria(self, filters):
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            cursor = self.db_manager.connection.cursor()
            query = """
                SELECT s.id, s.first_name, s.national_id, s.password, d.name as department_name
                FROM students s
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE 1=1
            """
            params = []
            if 'department' in filters and filters['department']:
                query += " AND s.department_id = (SELECT id FROM departments WHERE name = ?)"
                params.append(filters['department'])
            query += " ORDER BY s.id"
            cursor.execute(query, params)
            rows = cursor.fetchall()
            students = []
            for row in rows:
                students.append({
                    'id': row[0],
                    'first_name': row[1],
                    'national_id': row[2],
                    'password': row[3],
                    'department_name': row[4]
                })
            return students
        except Exception as e:
            print(f"خطأ في جلب الطلاب حسب المعايير: {e}")
            return []

    def get_all_passwords(self):
        """جلب جميع الأرقام السرية الحالية للطلاب"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT password FROM students WHERE password IS NOT NULL AND password != ''")
            rows = cursor.fetchall()
            return [row[0] for row in rows if row[0]]
        except Exception as e:
            print(f"خطأ في جلب جميع الأرقام السرية: {e}")
            return []

        # جدول الطلاب مع تحديد متعدد داخل إطار قابل للتمرير
        table_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        tree_scrollbar = ttk.Scrollbar(table_frame, orient="vertical")
        tree_scrollbar.pack(side="right", fill="y")

        columns = ("id", "الاسم الأول", "الرقم القومي", "القسم", "الحالة", "الرقم السري الحالي")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=16, selectmode='extended', yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.config(command=tree.yview)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120 if col=="id" else 150) # تم زيادة عرض الأعمدة قليلاً
        tree.pack(side="left", fill=tk.BOTH, expand=True)

        # تحميل الطلاب
        students = self.db_manager.get_students()
        for student in students:
            tree.insert('', tk.END, values=(
                student['id'],
                student.get('first_name', ''),
                student.get('national_id', ''),
                student.get('department_name', ''),
                student.get('status', ''),
                student.get('password', '')
            ))

        # إدخال الرقم السري الجديد
        pw_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        pw_frame.pack(pady=10)
        tk.Label(pw_frame, text="الرقم السري الجديد:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).pack(side=tk.LEFT, padx=5)
        password_var = tk.StringVar()
        pw_entry = tk.Entry(pw_frame, textvariable=password_var, font=('Segoe UI', 13), width=22)
        pw_entry.pack(side=tk.LEFT, padx=5)

        def set_passwords():
            selected = tree.selection()
            new_pw = password_var.get().strip()
            if not selected:
                messagebox.showwarning("تنبيه", "يرجى تحديد طالب واحد على الأقل.")
                return
            if not new_pw:
                messagebox.showwarning("تنبيه", "يرجى إدخال الرقم السري الجديد.")
                return
            ids = [tree.item(item)['values'][0] for item in selected]
            try:
                for sid in ids:
                    self.db_manager.execute_update("UPDATE students SET password=? WHERE id=?", (new_pw, sid))
                messagebox.showinfo("نجاح", f"تم تعيين الرقم السري الجديد لعدد {len(ids)} طالب/ة.")
                # تحديث الجدول
                for item in selected:
                    vals = list(tree.item(item)['values'])
                    vals[-1] = new_pw
                    tree.item(item, values=vals)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تعيين الرقم السري: {e}")

        btn = tk.Button(win, text="تعيين الرقم السري", command=set_passwords, bg='#2563eb', fg='white', font=('Segoe UI', 13, 'bold'), width=18)
        btn.pack(pady=10)

        tk.Button(win, text="إغلاق", command=win.destroy, bg='#64748b', fg='white', font=('Segoe UI', 11), width=10).pack(pady=5)
        
    def setup_table_section(self, parent):
        """إعداد قسم الجدول"""
        # بطاقة البحث والتصفية
        search_card = ModernCard(parent, title="البحث والتصفية")
        search_card.pack(fill=tk.X, pady=(0, 20))
        
        # شريط البحث
        search_frame = tk.Frame(search_card.content_frame, bg=theme.colors['bg_card'])
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل البحث
        search_label = tk.Label(search_frame,
                               text="البحث:",
                               font=theme.fonts['body_medium_bold'],
                               bg=theme.colors['bg_card'],
                               fg=theme.colors['text_primary'])
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=theme.fonts['body_medium'],
                               bg=theme.colors['bg_card'],
                               fg=theme.colors['text_primary'],
                               relief='solid',
                               bd=1,
                               width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.filter_students)
        
        # زر البحث
        ModernButton(search_frame, "بحث", self.search_students, 'primary', '🔍', 'small').pack(side=tk.LEFT)
        
        # بطاقة الجدول
        table_card = ModernCard(parent, title="قائمة الطلاب")
        table_card.pack(fill=tk.BOTH, expand=True)
        
        # الجدول مع الأعمدة المحدثة (بدون البريد الإلكتروني)
        columns = ['ID', 'الاسم الأول', 'الرقم القومي', 'رقم الجلوس', 'تاريخ الميلاد', 'النوع', 'السن', 'المحافظة', 'القسم', 'الهاتف']
        # تم حذف أي أعمدة تخص رمز القسم أو وصف القسم
        self.table = ModernTable(table_card.content_frame, columns, height=15)
        self.table.pack(fill=tk.BOTH, expand=True)
        
        # ربط حدث التحديد
        self.table.tree.bind('<<TreeviewSelect>>', self.on_student_select)
        
    def on_national_id_change(self, event=None):
        """معالج تغيير الرقم القومي لاستخراج البيانات تلقائياً"""
        try:
            national_id = self.form.get_value("الرقم القومي")
            if not national_id or len(national_id) != 14:
                # مسح الحقول المستخرجة إذا كان الرقم غير صحيح
                self.form.set_value("تاريخ الميلاد", "")
                self.form.set_value("النوع", "")
                self.form.set_value("المحافظة", "")
                self.form.set_value("السن", "")
                return
            
            # استخراج البيانات من الرقم القومي المصري
            parsed_data = EgyptianIDParser.parse_egyptian_id(national_id)
            
            if parsed_data['valid']:
                # تحديث الحقول المستخرجة
                if parsed_data['birth_date']:
                    self.form.set_value("تاريخ الميلاد", parsed_data['birth_date'].strftime('%Y-%m-%d'))
                
                if parsed_data['gender']:
                    self.form.set_value("النوع", parsed_data['gender'])
                
                if parsed_data['governorate']:
                    self.form.set_value("المحافظة", parsed_data['governorate'])
                
                if parsed_data['age'] is not None:
                    self.form.set_value("السن", parsed_data['age'])
            else:
                # مسح الحقول إذا كان الرقم غير صحيح
                self.form.set_value("تاريخ الميلاد", "")
                self.form.set_value("النوع", "")
                self.form.set_value("المحافظة", "")
                self.form.set_value("السن", "")
                
        except Exception as e:
            print(f"خطأ في معالجة الرقم القومي: {e}")

    def get_departments_list(self):
        """الحصول على قائمة الأقسام (بدون رمز القسم أو الوصف)"""
        try:
            departments = self.db_manager.get_departments()
            # فقط اسم القسم
            return [dept['name'] for dept in departments]
        except:
            return []
            
    def add_student(self):
        """إضافة طالب جديد مع التحقق المحسن"""
        try:
            if not self.db_manager or not hasattr(self.db_manager, 'connection'):
                messagebox.showerror("خطأ في الاتصال", "قاعدة البيانات غير متصلة!")
                return
            values = self.form.get_values()
            name = values.get('الاسم الأول', '').strip()
            national_id = values.get('الرقم القومي', '').strip()
            seat_number = values.get('رقم الجلوس', '').strip()
            password = values.get('الرقم السري', '').strip()
            phone = values.get('رقم الهاتف', '').strip()
            address = values.get('العنوان', '').strip()
            department = values.get('القسم', '').strip()
            # التحقق من صحة البيانات
            if not name:
                messagebox.showerror("خطأ في الإدخال", "الاسم الأول مطلوب!")
                return
            if not national_id:
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي مطلوب!")
                return
            if len(name) < 2:
                messagebox.showerror("خطأ في الإدخال", "الاسم الأول يجب أن يكون على الأقل حرفين.")
                return
            if not national_id.isdigit() or len(national_id) != 14:
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي يجب أن يكون 14 رقمًا.")
                return
            # التحقق من صحة الرقم القومي
            if not EgyptianIDParser.is_valid_egyptian_id(national_id):
                messagebox.showerror("خطأ في الإدخال", "الرقم القومي غير صحيح! يجب أن يكون 14 رقمًا.")
                return
            parsed_data = EgyptianIDParser.parse_egyptian_id(national_id)
            department_id = self.get_department_id(department)
            student_data = {
                'first_name': name,
                'national_id': national_id,
                'seat_number': seat_number,
                'password': password,
                'birth_date': parsed_data.get('birth_date').strftime('%Y-%m-%d') if parsed_data.get('birth_date') else '',
                'gender': parsed_data.get('gender', ''),
                'governorate': parsed_data.get('governorate', ''),
                'age': parsed_data.get('age', ''),
                'phone': phone,
                'department_id': department_id,
                'address': address
            }
            try:
                student_id = self.save_student_to_db(student_data)
                success_message = f"""تم حفظ الطالب بنجاح!\n\nالاسم: {name}\nالرقم القومي: {national_id}\nرقم الجلوس: {seat_number}\nتاريخ الميلاد: {parsed_data.get('birth_date', '')}\nالنوع: {parsed_data.get('gender', '')}\nالمحافظة: {parsed_data.get('governorate', '')}\nالسن: {parsed_data.get('age', '')}\nمعرف الطالب في النظام: {student_id}"""
                messagebox.showinfo("تم الحفظ بنجاح", success_message)
                self.refresh_data()
                self.clear_form()
            except Exception as db_error:
                messagebox.showerror("خطأ في الحفظ", f"فشل في حفظ البيانات: {str(db_error)}")
        except Exception as e:
            messagebox.showerror("خطأ غير متوقع", f"فشل في معالجة بيانات الطالب: {str(e)}")
    
    def save_student_to_db(self, student_data):
        """حفظ بيانات الطالب في قاعدة البيانات"""
        try:
            # التأكد من أن قاعدة البيانات متصلة
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            # استخدام الدالة الجديدة المحدثة
            if hasattr(self.db_manager, 'add_student_new'):
                student_id = self.db_manager.add_student_new(student_data)
                return student_id
            else:
                # الطريقة البديلة
                query = """
                    INSERT INTO students (
                        first_name, national_id, seat_number, password, 
                        birth_date, gender, governorate, age, phone, 
                        department_id, address, enrollment_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, date('now'), 'نشط')
                """
                
                params = (
                    student_data['first_name'],
                    student_data['national_id'],
                    student_data['seat_number'],
                    student_data['password'],
                    student_data['birth_date'],
                    student_data['gender'],
                    student_data['governorate'],
                    student_data['age'],
                    student_data['phone'],
                    student_data['department_id'],
                    student_data['address']
                )
                
                cursor = self.db_manager.connection.cursor()
                cursor.execute(query, params)
                self.db_manager.connection.commit()
                student_id = cursor.lastrowid
                return student_id
                
        except Exception as e:
            raise Exception(f"خطأ في قاعدة البيانات: {str(e)}")
    

            
    def delete_student_from_db(self, student_id):
        """حذف الطالب من قاعدة البيانات"""
        try:
            # التأكد من أن قاعدة البيانات متصلة
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            # استعلام الحذف
            query = "DELETE FROM students WHERE id = ?"
            
            cursor = self.db_manager.connection.cursor()
            cursor.execute(query, (student_id,))
            self.db_manager.connection.commit()
                
        except Exception as e:
            raise Exception(f"خطأ في حذف الطالب من قاعدة البيانات: {str(e)}")
            

            
    def delete_student(self):
        """حذف الطالب المحدد"""
        selected = self.table.tree.selection()
        if not selected:
            messagebox.showerror("خطأ", "يرجى تحديد طالب للحذف!")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الطالب؟"):
            try:
                # الحصول على معرف الطالب المحدد
                item = self.table.tree.item(selected[0])
                student_id = item['values'][0]
                
                # حذف الطالب من قاعدة البيانات
                self.delete_student_from_db(student_id)
                messagebox.showinfo("نجح", "تم حذف الطالب بنجاح!")
                self.refresh_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الطالب: {str(e)}")
                
    def clear_form(self):
        """مسح النموذج"""
        self.form.clear()
        
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # مسح الجدول
            self.table.clear()
            
            # تحميل الطلاب من قاعدة البيانات
            students = self.get_students_from_db()
            
            for student in students:
                # الحصول على اسم القسم
                department_name = self.get_department_name(student.get('department_id'))
                
                self.table.insert_row([
                    student.get('id', ''),
                    student.get('first_name', ''),
                    student.get('national_id', ''),
                    student.get('seat_number', ''),
                    student.get('birth_date', ''),
                    student.get('gender', ''),
                    student.get('age', ''),
                    student.get('governorate', ''),
                    department_name,
                    student.get('phone', '')
                ])
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def get_students_from_db(self):
        """جلب الطلاب من قاعدة البيانات"""
        try:
            # التأكد من الاتصال
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.connection.cursor()
            
            # التحقق من وجود الأعمدة الجديدة في جدول students
            cursor.execute("PRAGMA table_info(students)")
            columns_info = cursor.fetchall()
            columns = [column[1] for column in columns_info]
            
            # إنشاء استعلام حسب الأعمدة المتاحة
            if 'national_id' in columns and 'seat_number' in columns:
                query = """
                    SELECT id, first_name, national_id, seat_number, birth_date, 
                           gender, governorate, age, phone, department_id, address
                    FROM students 
                    ORDER BY id DESC
                """
            else:
                # استخدام الهيكل القديم إذا لم تكن الأعمدة الجديدة موجودة
                query = """
                    SELECT id, 
                           COALESCE(first_name, '') as first_name,
                           COALESCE(student_id, '') as national_id, 
                           '' as seat_number, 
                           COALESCE(date_of_birth, '') as birth_date,
                           COALESCE(gender, '') as gender, 
                           '' as governorate, 
                           '' as age, 
                           COALESCE(phone, '') as phone, 
                           COALESCE(section_id, department_id, '') as department_id, 
                           COALESCE(address, '') as address
                    FROM students 
                    ORDER BY id DESC
                """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            print(f"تم جلب {len(rows)} طالب من قاعدة البيانات")  # للتشخيص
            
            # تحويل النتائج إلى قاموس
            students = []
            for row in rows:
                student = {
                    'id': row[0],
                    'first_name': row[1],
                    'national_id': row[2],
                    'seat_number': row[3],
                    'birth_date': row[4],
                    'gender': row[5],
                    'governorate': row[6],
                    'age': row[7],
                    'phone': row[8],
                    'department_id': row[9],
                    'address': row[10] if len(row) > 10 else ''
                }
                students.append(student)
                print(f"طالب: {student}")  # للتشخيص
            
            return students
                
        except Exception as e:
            print(f"خطأ في جلب البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
            
    def filter_students(self, event=None):
        """تصفية الطلاب"""
        search_term = self.search_var.get().lower()
        
        # TODO: تنفيذ التصفية
        pass
        
    def search_students(self):
        """البحث عن الطلاب"""
        search_term = self.search_var.get()
        if not search_term:
            self.refresh_data()
            return
            
        # TODO: تنفيذ البحث
        pass
        
    def on_student_select(self, event):
        """عند تحديد طالب من الجدول"""
        selected = self.table.tree.selection()
        if not selected:
            return
            
        try:
            # الحصول على بيانات الطالب المحدد
            item = self.table.tree.item(selected[0])
            student_id = item['values'][0]
            
            # تحميل بيانات الطالب في النموذج
            # TODO: تحميل البيانات في النموذج
            pass
            
        except Exception as e:
            print(f"خطأ في تحديد الطالب: {e}")
            
    def get_department_id(self, department_name):
        """الحصول على معرف القسم (بدون رمز القسم أو الوصف)"""
        try:
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept['name'] == department_name:
                    return dept['id']
            return None
        except:
            return None
            
    def get_department_name(self, department_id):
        """الحصول على اسم القسم (بدون رمز القسم أو الوصف)"""
        try:
            departments = self.db_manager.get_departments()
            for dept in departments:
                if dept['id'] == department_id:
                    return dept['name']
            return 'غير محدد'
        except:
            return 'غير محدد'
            
    def show_statistics(self):
        """عرض إحصائيات الطلاب"""
        messagebox.showinfo("إحصائيات", "ميزة الإحصائيات ستتوفر قريباً!")
        
    def export_students(self):
        """تصدير بيانات الطلاب"""
        messagebox.showinfo("تصدير", "ميزة التصدير ستتوفر قريباً!")
        
    def import_students(self):
        """استيراد بيانات الطلاب"""
        messagebox.showinfo("استيراد", "ميزة الاستيراد ستتوفر قريباً!")
        
    def on_student_select(self, event):
        """معالج تحديد الطالب من الجدول"""
        try:
            selected = self.table.tree.selection()
            if selected:
                # الحصول على بيانات الطالب المحدد
                item = self.table.tree.item(selected[0])
                values = item['values']
                
                # تعبئة النموذج ببيانات الطالب
                if len(values) >= 10:
                    self.form.set_value("الاسم الأول", values[1] if values[1] else '')
                    self.form.set_value("الرقم القومي", values[2] if values[2] else '')
                    self.form.set_value("رقم الجلوس", values[3] if values[3] else '')
                    self.form.set_value("تاريخ الميلاد", values[4] if values[4] else '')
                    self.form.set_value("النوع", values[5] if values[5] else '')
                    self.form.set_value("السن", values[6] if values[6] else '')
                    self.form.set_value("المحافظة", values[7] if values[7] else '')
                    self.form.set_value("رقم الهاتف", values[9] if values[9] else '')
                    
                    # البحث عن القسم
                    department_name = values[8] if values[8] else ''
                    if department_name:
                        self.form.set_value("القسم", department_name)
                    
                    # جلب بيانات إضافية من قاعدة البيانات
                    student_id = values[0]
                    additional_data = self.get_additional_student_data(student_id)
                    if additional_data:
                        self.form.set_value("الرقم السري", additional_data.get('password', ''))
                        self.form.set_value("العنوان", additional_data.get('address', ''))
                        
        except Exception as e:
            print(f"خطأ في تحديد الطالب: {e}")
            
    def get_additional_student_data(self, student_id):
        """جلب بيانات إضافية للطالب"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
                
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT password, address FROM students WHERE id = ?", (student_id,))
            result = cursor.fetchone()
            
            if result:
                return {
                    'password': result[0] if result[0] else '',
                    'address': result[1] if result[1] else ''
                }
            return {}
            
        except Exception as e:
            print(f"خطأ في جلب البيانات الإضافية: {e}")
            return {}
    
    def search_students(self):
        """البحث في الطلاب"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.refresh_data()
            return
            
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
                
            cursor = self.db_manager.connection.cursor()
            
            # البحث في عدة حقول
            query = """
                SELECT id, first_name, national_id, seat_number, birth_date, 
                       gender, governorate, age, phone, department_id, address
                FROM students 
                WHERE first_name LIKE ? OR national_id LIKE ? OR seat_number LIKE ? OR phone LIKE ?
                ORDER BY id DESC
            """
            
            search_pattern = f"%{search_term}%"
            cursor.execute(query, (search_pattern, search_pattern, search_pattern, search_pattern))
            students = cursor.fetchall()
            
            # مسح الجدول الحالي
            self.table.clear()
            
            # إضافة النتائج
            for student in students:
                department_name = self.get_department_name(student[9]) if student[9] else ''
                self.table.insert_row([
                    student[0],  # ID
                    student[1],  # first_name
                    student[2],  # national_id
                    student[3],  # seat_number
                    student[4],  # birth_date
                    student[5],  # gender
                    student[7],  # age
                    student[6],  # governorate
                    department_name,  # department_name
                    student[8]   # phone
                ])
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def filter_students(self, event=None):
        """تصفية الطلاب أثناء الكتابة"""
        # استدعاء البحث مع تأخير صغير
        self.parent.after(300, self.search_students)

    def distribute_passwords(self, students, password_value):
        """توزيع الرقم السري كما هو على جميع الطلاب المحددين"""
        for student in students:
            self.db_manager.execute_update(
                "UPDATE students SET password=? WHERE id=?",
                (password_value, student['id'])
            )

    def open_auto_password_distribution_window(self, parent_win):
        """نافذة توزيع الأرقام السرية حسب التخصص فقط"""
        win = tk.Toplevel(parent_win)
        win.title("توزيع تلقائي للأرقام السرية")
        win.geometry("800x600")
        win.transient(parent_win)
        win.grab_set()
        win.resizable(True, True)
        win.configure(bg=theme.colors['bg_card'])

        tk.Label(win, text="توزيع الأرقام السرية تلقائيًا حسب التخصص:", font=('Segoe UI', 15, 'bold'), bg=theme.colors['bg_card'], fg=theme.colors['primary']).pack(pady=15)

        # إطار المعايير
        filter_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        filter_frame.pack(fill=tk.X, padx=20, pady=5)

        # جلب القيم من قاعدة البيانات
        departments = self.get_departments_list()

        # متغيرات الاختيار
        dept_var = tk.StringVar()
        group_size_var = tk.IntVar(value=10)
        pw_prefix_var = tk.StringVar(value="STU")

        # القسم
        tk.Label(filter_frame, text="القسم:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=0, column=0, padx=5, pady=5, sticky='e')
        dept_cb = ttk.Combobox(filter_frame, textvariable=dept_var, values=departments, width=18, state="readonly")
        dept_cb.grid(row=0, column=1, padx=5, pady=5)

        # حجم المجموعة
        tk.Label(filter_frame, text="عدد الطلاب في كل مجموعة:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=1, column=0, padx=5, pady=5, sticky='e')
        group_size_entry = tk.Entry(filter_frame, textvariable=group_size_var, width=8, font=('Segoe UI', 12))
        group_size_entry.grid(row=1, column=1, padx=5, pady=5)

        # بادئة الرقم السري
        tk.Label(filter_frame, text="بادئة الرقم السري:", font=('Segoe UI', 12), bg=theme.colors['bg_card']).grid(row=1, column=2, padx=5, pady=5, sticky='e')
        pw_prefix_entry = tk.Entry(filter_frame, textvariable=pw_prefix_var, width=10, font=('Segoe UI', 12))
        pw_prefix_entry.grid(row=1, column=3, padx=5, pady=5)

        # زر تصفية الطلاب
        def update_students_table():
            tree.delete(*tree.get_children())
            filters = {}
            if dept_var.get():
                filters['department'] = dept_var.get()
            students = self.get_students_by_criteria(filters)
            for student in students:
                tree.insert('', tk.END, values=(
                    student['id'], student.get('first_name',''), student.get('national_id',''),
                    student.get('department_name',''),
                    student.get('password',''), ''
                ))

        filter_btn = tk.Button(filter_frame, text="تصفية الطلاب", command=update_students_table, bg='#2563eb', fg='white', font=('Segoe UI', 11, 'bold'), width=12)
        filter_btn.grid(row=1, column=4, padx=5, pady=5)

        # جدول الطلاب المستهدفين
        table_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        tree_scrollbar = ttk.Scrollbar(table_frame, orient="vertical")
        tree_scrollbar.pack(side="right", fill="y")
        columns = ("id", "الاسم الأول", "الرقم القومي", "القسم", "الرقم السري الحالي", "الرقم السري الجديد")
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=16, selectmode='extended', yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.config(command=tree.yview)
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120 if col=="id" else 140)
        tree.pack(side="left", fill=tk.BOTH, expand=True)

        # زر توزيع الأرقام السرية
        def distribute_passwords():
            students = [tree.item(item)['values'] for item in tree.get_children()]
            if not students:
                messagebox.showwarning("تنبيه", "لا يوجد طلاب لتوزيع الأرقام السرية عليهم.")
                return
            prefix = pw_prefix_var.get() or "STU"
            for item in tree.get_children():
                vals = list(tree.item(item)['values'])
                vals[-1] = prefix  # الرقم السري كما أدخلته فقط
                tree.item(item, values=vals)

        # زر حفظ الأرقام السرية في قاعدة البيانات
        def save_passwords():
            updated = 0
            for item in tree.get_children():
                vals = tree.item(item)['values']
                sid = vals[0]
                new_pw = vals[-1]
                if new_pw:
                    try:
                        self.db_manager.execute_update("UPDATE students SET password=? WHERE id=?", (new_pw, sid))
                        updated += 1
                    except Exception as e:
                        print(f"خطأ في تحديث كلمة السر للطالب {sid}: {e}")
            messagebox.showinfo("تم الحفظ", f"تم تحديث الأرقام السرية لعدد {updated} طالب/ة.")
            update_students_table()

        # أزرار العمليات
        ops_frame = tk.Frame(win, bg=theme.colors['bg_card'])
        ops_frame.pack(pady=10)
        tk.Button(ops_frame, text="توزيع الأرقام السرية", command=distribute_passwords, bg='#059669', fg='white', font=('Segoe UI', 12, 'bold'), width=18).pack(side=tk.LEFT, padx=8)