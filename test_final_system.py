#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للنظام المحسن
Final test for enhanced system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_final_system():
    """اختبار نهائي شامل للنظام"""
    
    print("🎯 الاختبار النهائي لنظام المواد المحسن")
    print("=" * 60)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # 1. عرض حالة النظام الحالية
        print("📊 حالة النظام الحالية:")
        print("-" * 40)
        
        cursor.execute("SELECT COUNT(*) FROM subjects")
        total_subjects = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM subjects WHERE is_cultural = 1")
        cultural_subjects = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM subjects WHERE is_cultural = 0")
        specialized_subjects = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM departments")
        total_departments = cursor.fetchone()[0]
        
        print(f"• إجمالي المواد: {total_subjects}")
        print(f"• المواد الثقافية: {cultural_subjects}")
        print(f"• المواد التخصصية: {specialized_subjects}")
        print(f"• إجمالي الأقسام: {total_departments}")
        
        # 2. اختبار إضافة مادة ثقافية جديدة
        print(f"\n🧪 اختبار 1: إضافة مادة ثقافية")
        print("-" * 40)
        
        try:
            cursor.execute("""
                INSERT OR IGNORE INTO subjects 
                (name, code, department_id, subject_category, is_cultural, include_in_total, total_degree, description)
                VALUES (?, ?, NULL, ?, ?, ?, ?, ?)
            """, (
                "علم الاجتماع",
                "SO101", 
                "ثقافية",
                1,
                1,
                100,
                "مادة علم الاجتماع العامة"
            ))
            
            if cursor.rowcount > 0:
                print("✅ تم إضافة المادة الثقافية بنجاح!")
                
                # ربطها بجميع الأقسام
                cursor.execute("SELECT id FROM subjects WHERE name = 'علم الاجتماع'")
                subject_id = cursor.fetchone()[0]
                
                cursor.execute("SELECT id, name FROM departments")
                departments = cursor.fetchall()
                
                for dept_id, dept_name in departments:
                    cursor.execute("""
                        INSERT OR IGNORE INTO department_subjects 
                        (department_id, subject_id, units, is_required)
                        VALUES (?, ?, 2, 1)
                    """, (dept_id, subject_id))
                
                print(f"✅ تم ربط المادة بـ {len(departments)} أقسام!")
            else:
                print("ℹ️ المادة موجودة بالفعل")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة المادة الثقافية: {e}")
        
        # 3. اختبار إضافة مادة تخصصية
        print(f"\n🧪 اختبار 2: إضافة مادة تخصصية")
        print("-" * 40)
        
        try:
            # الحصول على قسم الحاسب الآلي
            cursor.execute("SELECT id FROM departments WHERE name LIKE '%Computer%' OR name LIKE '%حاسب%' LIMIT 1")
            cs_dept = cursor.fetchone()
            
            if cs_dept:
                cs_dept_id = cs_dept[0]
                
                cursor.execute("""
                    INSERT OR IGNORE INTO subjects 
                    (name, code, department_id, subject_category, is_cultural, include_in_total, total_degree, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    "تطوير تطبيقات الويب",
                    "CS502", 
                    cs_dept_id,
                    "تخصصية",
                    0,
                    1,
                    100,
                    "مادة تطوير تطبيقات الويب المتقدمة"
                ))
                
                if cursor.rowcount > 0:
                    print("✅ تم إضافة المادة التخصصية بنجاح!")
                else:
                    print("ℹ️ المادة موجودة بالفعل")
            else:
                print("❌ لم يتم العثور على قسم الحاسب الآلي")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة المادة التخصصية: {e}")
        
        # 4. عرض المواد حسب الأقسام
        print(f"\n📋 عرض المواد حسب الأقسام:")
        print("-" * 60)
        
        cursor.execute("SELECT id, name FROM departments ORDER BY name")
        departments = cursor.fetchall()
        
        for dept_id, dept_name in departments:
            print(f"\n🏢 {dept_name}:")
            
            # المواد التخصصية للقسم
            cursor.execute("""
                SELECT name, code, units FROM subjects 
                WHERE department_id = ? AND is_cultural = 0
                ORDER BY name
            """, (dept_id,))
            specialized = cursor.fetchall()
            
            # المواد الثقافية المرتبطة بالقسم
            cursor.execute("""
                SELECT s.name, s.code, ds.units 
                FROM subjects s
                JOIN department_subjects ds ON s.id = ds.subject_id
                WHERE ds.department_id = ? AND s.is_cultural = 1
                ORDER BY s.name
            """, (dept_id,))
            cultural = cursor.fetchall()
            
            print(f"   📚 المواد التخصصية ({len(specialized)}):")
            for name, code, units in specialized:
                code_str = f"({code})" if code else ""
                units_str = f"{units} وحدات" if units else "وحدات غير محددة"
                print(f"     • {name} {code_str} - {units_str}")
            
            print(f"   🌍 المواد الثقافية ({len(cultural)}):")
            for name, code, units in cultural:
                code_str = f"({code})" if code else ""
                units_str = f"{units} وحدات" if units else "وحدات غير محددة"
                print(f"     • {name} {code_str} - {units_str}")
        
        # 5. إحصائيات نهائية
        db_manager.connection.commit()
        
        cursor.execute("SELECT COUNT(*) FROM subjects")
        final_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM subjects WHERE is_cultural = 1")
        final_cultural = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM department_subjects")
        total_links = cursor.fetchone()[0]
        
        print(f"\n" + "=" * 60)
        print("📊 الإحصائيات النهائية:")
        print("-" * 40)
        print(f"• إجمالي المواد: {final_total}")
        print(f"• المواد الثقافية: {final_cultural}")
        print(f"• المواد التخصصية: {final_total - final_cultural}")
        print(f"• روابط المواد بالأقسام: {total_links}")
        
        print(f"\n✅ تم الانتهاء من الاختبار النهائي بنجاح!")
        print("🎉 النظام جاهز للاستخدام!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    test_final_system()
