#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص مشكلة الفصول
Check classes issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def check_classes_issue():
    """فحص مشكلة تحميل الفصول"""
    
    print("🔍 فحص مشكلة تحميل الفصول")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # 1. فحص الجداول الموجودة
        print("📋 فحص الجداول الموجودة:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        required_tables = ['classes', 'sections', 'subjects', 'departments']
        for table in required_tables:
            if table in table_names:
                print(f"  ✅ {table}")
            else:
                print(f"  ❌ {table} - مفقود!")
        
        # 2. فحص بيانات كل جدول
        print(f"\n📊 فحص البيانات:")
        
        for table in required_tables:
            if table in table_names:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  • {table}: {count} سجل")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    sample = cursor.fetchall()
                    print(f"    عينة من البيانات: {len(sample)} سجل")
        
        # 3. اختبار استعلام الفصول المبسط
        print(f"\n🧪 اختبار استعلام الفصول:")
        
        try:
            # استعلام بسيط أولاً
            cursor.execute("SELECT * FROM classes LIMIT 5")
            classes = cursor.fetchall()
            print(f"  ✅ استعلام الفصول البسيط: {len(classes)} فصل")
            
            if classes:
                print("    عينة من الفصول:")
                for cls in classes[:3]:
                    print(f"      - {cls}")
            
        except Exception as e:
            print(f"  ❌ خطأ في استعلام الفصول البسيط: {e}")
        
        # 4. اختبار الاستعلام المعقد
        try:
            query = """
                SELECT c.*, sec.name as section_name, sub.name as subject_name, sub.code as subject_code,
                       d.name as department_name
                FROM classes c
                LEFT JOIN sections sec ON c.section_id = sec.id
                LEFT JOIN subjects sub ON c.subject_id = sub.id
                LEFT JOIN departments d ON sec.department_id = d.id
                ORDER BY c.academic_year DESC, c.semester, sub.name
                LIMIT 5
            """
            cursor.execute(query)
            complex_classes = cursor.fetchall()
            print(f"  ✅ استعلام الفصول المعقد: {len(complex_classes)} فصل")
            
            if complex_classes:
                print("    عينة من النتائج:")
                for cls in complex_classes[:2]:
                    print(f"      - {cls}")
            
        except Exception as e:
            print(f"  ❌ خطأ في استعلام الفصول المعقد: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. اختبار دالة get_classes
        print(f"\n🎯 اختبار دالة get_classes:")
        try:
            classes = db_manager.get_classes()
            print(f"  ✅ دالة get_classes: {len(classes)} فصل")
            
            if classes:
                print("    عينة من النتائج:")
                for cls in classes[:2]:
                    print(f"      - {cls}")
            
        except Exception as e:
            print(f"  ❌ خطأ في دالة get_classes: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. إنشاء بيانات تجريبية إذا لزم الأمر
        print(f"\n🔧 فحص الحاجة لبيانات تجريبية:")
        
        cursor.execute("SELECT COUNT(*) FROM classes")
        classes_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sections")
        sections_count = cursor.fetchone()[0]
        
        if classes_count == 0 and sections_count > 0:
            print("  📝 إنشاء فصل تجريبي...")
            try:
                # الحصول على أول شعبة ومادة
                cursor.execute("SELECT id FROM sections LIMIT 1")
                section_result = cursor.fetchone()
                
                cursor.execute("SELECT id FROM subjects LIMIT 1")
                subject_result = cursor.fetchone()
                
                if section_result and subject_result:
                    cursor.execute("""
                        INSERT INTO classes (name, section_id, subject_id, teacher_name, 
                                           schedule, room_number, semester, academic_year)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        "فصل تجريبي",
                        section_result[0],
                        subject_result[0],
                        "مدرس تجريبي",
                        "الأحد 8:00-10:00",
                        "قاعة 101",
                        "الفصل الأول",
                        "2024-2025"
                    ))
                    
                    db_manager.connection.commit()
                    print("  ✅ تم إنشاء فصل تجريبي!")
                
            except Exception as e:
                print(f"  ❌ خطأ في إنشاء فصل تجريبي: {e}")
        
        print(f"\n" + "=" * 50)
        print("✅ انتهى فحص مشكلة الفصول")
        
    except Exception as e:
        print(f"❌ خطأ عام في الفحص: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    check_classes_issue()
