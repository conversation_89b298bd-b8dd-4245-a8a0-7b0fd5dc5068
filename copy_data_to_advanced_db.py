#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ البيانات من قاعدة البيانات العادية إلى المتقدمة
Copy data from regular database to advanced database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from database.advanced_db_manager import AdvancedDatabaseManager

def copy_data_to_advanced_db():
    """نسخ البيانات من قاعدة البيانات العادية إلى المتقدمة"""
    
    print("📋 نسخ البيانات إلى قاعدة البيانات المتقدمة")
    print("=" * 60)
    
    # الاتصال بقاعدة البيانات العادية
    print("🔗 الاتصال بقاعدة البيانات العادية...")
    regular_db = DatabaseManager()
    regular_db.connect()
    
    # الاتصال بقاعدة البيانات المتقدمة
    print("🔗 الاتصال بقاعدة البيانات المتقدمة...")
    advanced_db = AdvancedDatabaseManager()
    advanced_db.initialize_database()
    
    try:
        # 1. نسخ الأقسام
        print("\n📚 نسخ الأقسام...")
        departments = regular_db.execute_query("SELECT * FROM departments")
        print(f"  📊 عدد الأقسام في قاعدة البيانات العادية: {len(departments)}")
        
        for dept in departments:
            try:
                advanced_db.execute_update(
                    "INSERT OR REPLACE INTO departments (id, name, code, description, created_at) VALUES (?, ?, ?, ?, ?)",
                    (dept['id'], dept['name'], dept.get('code', ''), dept.get('description', ''), dept.get('created_at', ''))
                )
            except Exception as e:
                print(f"  ⚠️ خطأ في نسخ القسم {dept['name']}: {e}")
        
        # التحقق من النسخ
        advanced_departments = advanced_db.execute_query("SELECT COUNT(*) FROM departments")
        dept_count = advanced_departments[0]['COUNT(*)'] if advanced_departments else 0
        print(f"  ✅ تم نسخ {dept_count} قسم")
        
        # 2. نسخ المواد
        print("\n📖 نسخ المواد...")
        subjects = regular_db.execute_query("SELECT * FROM subjects")
        print(f"  📊 عدد المواد في قاعدة البيانات العادية: {len(subjects)}")
        
        for subject in subjects:
            try:
                # التحقق من وجود الأعمدة المطلوبة في قاعدة البيانات المتقدمة
                advanced_db.execute_update("""
                    INSERT OR REPLACE INTO subjects 
                    (id, name, code, department_id, subject_category, is_cultural, include_in_total, units, total_degree, description, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    subject['id'], 
                    subject['name'], 
                    subject.get('code', ''),
                    subject.get('department_id'),
                    subject.get('subject_category', 'ثقافية'),
                    subject.get('is_cultural', 1),
                    subject.get('include_in_total', 1),
                    subject.get('units', 3),
                    subject.get('total_degree', 100),
                    subject.get('description', ''),
                    subject.get('created_at', '')
                ))
            except Exception as e:
                print(f"  ⚠️ خطأ في نسخ المادة {subject['name']}: {e}")
        
        # التحقق من النسخ
        advanced_subjects = advanced_db.execute_query("SELECT COUNT(*) FROM subjects")
        subjects_count = advanced_subjects[0]['COUNT(*)'] if advanced_subjects else 0
        print(f"  ✅ تم نسخ {subjects_count} مادة")
        
        # 3. نسخ الشعب
        print("\n🏫 نسخ الشعب...")
        sections = regular_db.execute_query("SELECT * FROM sections")
        print(f"  📊 عدد الشعب في قاعدة البيانات العادية: {len(sections)}")
        
        for section in sections:
            try:
                advanced_db.execute_update(
                    "INSERT OR REPLACE INTO sections (id, name, department_id, capacity, created_at) VALUES (?, ?, ?, ?, ?)",
                    (section['id'], section['name'], section['department_id'], section.get('capacity', 30), section.get('created_at', ''))
                )
            except Exception as e:
                print(f"  ⚠️ خطأ في نسخ الشعبة {section['name']}: {e}")
        
        # التحقق من النسخ
        advanced_sections = advanced_db.execute_query("SELECT COUNT(*) FROM sections")
        sections_count = advanced_sections[0]['COUNT(*)'] if advanced_sections else 0
        print(f"  ✅ تم نسخ {sections_count} شعبة")
        
        # 4. نسخ الفصول
        print("\n🎓 نسخ الفصول...")
        classes = regular_db.execute_query("SELECT * FROM classes")
        print(f"  📊 عدد الفصول في قاعدة البيانات العادية: {len(classes)}")
        
        for cls in classes:
            try:
                advanced_db.execute_update("""
                    INSERT OR REPLACE INTO classes 
                    (id, name, section_id, subject_id, teacher_name, schedule, room_number, semester, academic_year, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    cls['id'], 
                    cls['name'], 
                    cls['section_id'], 
                    cls['subject_id'],
                    cls.get('teacher_name', ''),
                    cls.get('schedule', ''),
                    cls.get('room_number', ''),
                    cls.get('semester', ''),
                    cls.get('academic_year', ''),
                    cls.get('created_at', '')
                ))
            except Exception as e:
                print(f"  ⚠️ خطأ في نسخ الفصل {cls['name']}: {e}")
        
        # التحقق من النسخ
        advanced_classes = advanced_db.execute_query("SELECT COUNT(*) FROM classes")
        classes_count = advanced_classes[0]['COUNT(*)'] if advanced_classes else 0
        print(f"  ✅ تم نسخ {classes_count} فصل")
        
        # 5. نسخ ربط المواد بالأقسام
        print("\n🔗 نسخ ربط المواد بالأقسام...")
        try:
            dept_subjects = regular_db.execute_query("SELECT * FROM department_subjects")
            print(f"  📊 عدد الروابط في قاعدة البيانات العادية: {len(dept_subjects)}")
            
            for link in dept_subjects:
                try:
                    advanced_db.execute_update(
                        "INSERT OR REPLACE INTO department_subjects (department_id, subject_id, units) VALUES (?, ?, ?)",
                        (link['department_id'], link['subject_id'], link.get('units', 3))
                    )
                except Exception as e:
                    print(f"  ⚠️ خطأ في نسخ الربط: {e}")
            
            # التحقق من النسخ
            advanced_links = advanced_db.execute_query("SELECT COUNT(*) FROM department_subjects")
            links_count = advanced_links[0]['COUNT(*)'] if advanced_links else 0
            print(f"  ✅ تم نسخ {links_count} ربط")
            
        except Exception as e:
            print(f"  ⚠️ خطأ في نسخ روابط المواد: {e}")
        
        # 6. اختبار دالة get_classes في قاعدة البيانات المتقدمة
        print("\n🧪 اختبار دالة get_classes في قاعدة البيانات المتقدمة...")
        try:
            advanced_classes_result = advanced_db.get_classes()
            print(f"  ✅ get_classes(): {len(advanced_classes_result)} فصل")
            
            if advanced_classes_result:
                print("  📝 عينة من الفصول:")
                for i, cls in enumerate(advanced_classes_result[:3]):
                    print(f"    {i+1}. {cls.get('name', 'بدون اسم')} | {cls.get('section_name', 'بدون شعبة')} | {cls.get('subject_name', 'بدون مادة')}")
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار get_classes: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n" + "=" * 60)
        print("✅ تم نسخ جميع البيانات إلى قاعدة البيانات المتقدمة بنجاح!")
        print("🎯 يمكنك الآن تشغيل التطبيق الرئيسي والدخول على تبويب الفصول")
        
    except Exception as e:
        print(f"❌ خطأ عام في نسخ البيانات: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        regular_db.close()
        advanced_db.close()

if __name__ == "__main__":
    copy_data_to_advanced_db()
