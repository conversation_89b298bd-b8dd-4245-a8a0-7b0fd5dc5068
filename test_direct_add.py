#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لإضافة المواد
Direct test for adding subjects
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_direct_add():
    """اختبار مباشر لإضافة المواد"""
    
    print("🧪 اختبار مباشر لإضافة المواد")
    print("=" * 50)
    
    # إنشاء قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # عرض حالة قاعدة البيانات
    cursor = db_manager.connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM subjects")
    initial_count = cursor.fetchone()[0]
    print(f"📊 عدد المواد الحالية: {initial_count}")
    
    # عرض هيكل الجدول
    cursor.execute("PRAGMA table_info(subjects)")
    columns = cursor.fetchall()
    print(f"\n📋 أعمدة الجدول ({len(columns)}):")
    for col in columns:
        print(f"  - {col[1]} ({col[2]})")
    
    # اختبار إضافة مادة ثقافية
    print(f"\n🔬 اختبار 1: إضافة مادة ثقافية")
    print("-" * 40)
    
    try:
        cursor.execute("""
            INSERT INTO subjects (
                name, code, department_id, subject_category, 
                is_cultural, include_in_total, units, total_degree, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "علم النفس",
            "PS101", 
            1,
            "ثقافية",
            1,
            1,
            2,
            100,
            "مادة علم النفس التربوي"
        ))
        db_manager.connection.commit()
        print("✅ تم إضافة المادة الثقافية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المادة الثقافية: {e}")
    
    # اختبار إضافة مادة تخصصية
    print(f"\n🔬 اختبار 2: إضافة مادة تخصصية")
    print("-" * 40)
    
    try:
        cursor.execute("""
            INSERT INTO subjects (
                name, code, department_id, subject_category, 
                is_cultural, include_in_total, units, total_degree, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "الذكاء الاصطناعي",
            "CS501", 
            1,
            "تخصصية",
            0,
            1,
            4,
            100,
            "مادة الذكاء الاصطناعي المتقدمة"
        ))
        db_manager.connection.commit()
        print("✅ تم إضافة المادة التخصصية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المادة التخصصية: {e}")
    
    # اختبار إضافة مادة لا تُضاف للمجموع
    print(f"\n🔬 اختبار 3: إضافة مادة لا تُضاف للمجموع")
    print("-" * 40)
    
    try:
        cursor.execute("""
            INSERT INTO subjects (
                name, code, department_id, subject_category, 
                is_cultural, include_in_total, units, total_degree, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "الأنشطة الفنية",
            "AR101", 
            1,
            "ثقافية",
            1,
            0,  # لا تُضاف للمجموع
            1,
            50,
            "مادة الأنشطة الفنية والإبداعية"
        ))
        db_manager.connection.commit()
        print("✅ تم إضافة المادة (غير مُضافة للمجموع) بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المادة: {e}")
    
    # عرض النتائج النهائية
    print(f"\n" + "=" * 50)
    print("📊 النتائج النهائية:")
    print("-" * 40)
    
    cursor.execute("SELECT COUNT(*) FROM subjects")
    final_count = cursor.fetchone()[0]
    added_count = final_count - initial_count
    
    print(f"• عدد المواد قبل الاختبار: {initial_count}")
    print(f"• عدد المواد بعد الاختبار: {final_count}")
    print(f"• المواد المُضافة: {added_count}")
    
    # عرض جميع المواد
    print(f"\n📚 جميع المواد:")
    print("-" * 60)
    cursor.execute("""
        SELECT name, code, subject_category, is_cultural, include_in_total, units, total_degree 
        FROM subjects 
        ORDER BY is_cultural DESC, name
    """)
    
    all_subjects = cursor.fetchall()
    for i, subject in enumerate(all_subjects, 1):
        name = subject[0]
        code = subject[1] if subject[1] else "بدون رمز"
        category = subject[2] if subject[2] else "غير محدد"
        cultural = "ثقافية" if subject[3] else "تخصصية"
        in_total = "نعم" if subject[4] else "لا"
        units = subject[5] if subject[5] else "غير محدد"
        degree = subject[6] if subject[6] else "غير محدد"
        
        print(f"{i:2d}. {name} ({code})")
        print(f"     النوع: {cultural} | يُضاف للمجموع: {in_total} | الوحدات: {units} | الدرجة: {degree}")
        print()
    
    # اختبار محاكاة دالة save_subject_to_db
    print(f"🔬 اختبار 4: محاكاة دالة save_subject_to_db")
    print("-" * 40)
    
    test_subject_data = {
        'name': 'الإحصاء التطبيقي',
        'code': 'ST201',
        'department_id': 1,
        'subject_category': 'تخصصية',
        'is_cultural': 0,
        'include_in_total': 1,
        'units': 3,
        'total_degree': 100,
        'description': 'مادة الإحصاء التطبيقي في الحاسوب'
    }
    
    success = save_subject_to_db_simulation(db_manager, test_subject_data)
    if success:
        print("✅ نجحت محاكاة دالة save_subject_to_db!")
    else:
        print("❌ فشلت محاكاة دالة save_subject_to_db!")
    
    db_manager.close()
    print(f"\n✅ تم الانتهاء من الاختبار بنجاح!")

def save_subject_to_db_simulation(db_manager, subject_data):
    """محاكاة دالة save_subject_to_db"""
    try:
        if not db_manager.connection:
            db_manager.connect()
        
        # التحقق من الأعمدة الموجودة في الجدول
        cursor = db_manager.connection.cursor()
        cursor.execute("PRAGMA table_info(subjects)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # بناء استعلام الإدراج بناءً على الأعمدة الموجودة
        available_fields = []
        values = []
        
        field_mapping = {
            'name': subject_data['name'],
            'code': subject_data.get('code'),
            'department_id': subject_data.get('department_id'),
            'subject_category': subject_data.get('subject_category'),
            'is_cultural': subject_data.get('is_cultural'),
            'include_in_total': subject_data.get('include_in_total'),
            'units': subject_data.get('units'),
            'total_degree': subject_data.get('total_degree'),
            'description': subject_data.get('description')
        }
        
        for field, value in field_mapping.items():
            if field in columns and value is not None:
                available_fields.append(field)
                values.append(value)
        
        if not available_fields:
            raise Exception("لا توجد حقول صالحة للإدراج")
        
        placeholders = ', '.join(['?' for _ in available_fields])
        fields_str = ', '.join(available_fields)
        query = f"INSERT INTO subjects ({fields_str}) VALUES ({placeholders})"
        
        cursor.execute(query, values)
        db_manager.connection.commit()
        
        print(f"✅ تم إضافة المادة بنجاح: {subject_data['name']}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ المادة: {e}")
        return False

if __name__ == "__main__":
    test_direct_add()
