#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نظام المواد الثقافية لتكون متاحة لجميع الأقسام مع وحدات مختلفة
Fix cultural subjects system to be available for all departments with different units
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def fix_cultural_subjects_system():
    """إصلاح نظام المواد الثقافية"""
    
    print("🔧 إصلاح نظام المواد الثقافية")
    print("=" * 60)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # 1. تحديث المواد الثقافية الموجودة لتكون بدون قسم محدد
        print("📝 تحديث المواد الثقافية الموجودة...")
        cursor.execute("""
            UPDATE subjects 
            SET department_id = NULL 
            WHERE is_cultural = 1
        """)
        
        # 2. حذف الروابط القديمة للمواد الثقافية
        print("🗑️ حذف الروابط القديمة...")
        cursor.execute("""
            DELETE FROM department_subjects 
            WHERE subject_id IN (SELECT id FROM subjects WHERE is_cultural = 1)
        """)
        
        # 3. الحصول على جميع الأقسام
        cursor.execute("SELECT id, name FROM departments")
        departments = cursor.fetchall()
        print(f"📋 تم العثور على {len(departments)} أقسام")
        
        # 4. الحصول على المواد الثقافية
        cursor.execute("SELECT id, name FROM subjects WHERE is_cultural = 1")
        cultural_subjects = cursor.fetchall()
        print(f"📚 تم العثور على {len(cultural_subjects)} مواد ثقافية")
        
        # 5. تحديد عدد الوحدات لكل مادة ثقافية حسب القسم
        units_mapping = {
            "اللغة العربية": {
                "default": 2,
                "الحاسب الآلي": 2,
                "الإلكترونيات": 2, 
                "الميكانيكا": 2,
                "الكهرباء": 2,
                "المدني": 2,
                "المعمارية": 2
            },
            "اللغة الإنجليزية": {
                "default": 2,
                "الحاسب الآلي": 3,  # أكثر أهمية للحاسب
                "الإلكترونيات": 2,
                "الميكانيكا": 2,
                "الكهرباء": 2,
                "المدني": 2,
                "المعمارية": 2
            },
            "الرياضيات": {
                "default": 3,
                "الحاسب الآلي": 4,  # أساسية جداً للحاسب
                "الإلكترونيات": 4,  # أساسية للإلكترونيات
                "الميكانيكا": 4,    # أساسية للميكانيكا
                "الكهرباء": 4,     # أساسية للكهرباء
                "المدني": 3,       # مهمة للمدني
                "المعمارية": 3     # مهمة للمعمارية
            },
            "الفيزياء": {
                "default": 2,
                "الحاسب الآلي": 2,
                "الإلكترونيات": 3,  # مهمة للإلكترونيات
                "الميكانيكا": 3,    # مهمة للميكانيكا
                "الكهرباء": 3,     # مهمة للكهرباء
                "المدني": 2,
                "المعمارية": 2
            },
            "الكيمياء": {
                "default": 2,
                "الحاسب الآلي": 1,  # أقل أهمية للحاسب
                "الإلكترونيات": 2,
                "الميكانيكا": 2,
                "الكهرباء": 2,
                "المدني": 3,       # مهمة للمدني (مواد البناء)
                "المعمارية": 2
            },
            "التربية الدينية": {
                "default": 1,
                "الحاسب الآلي": 1,
                "الإلكترونيات": 1,
                "الميكانيكا": 1,
                "الكهرباء": 1,
                "المدني": 1,
                "المعمارية": 1
            },
            "التربية الرياضية": {
                "default": 1,
                "الحاسب الآلي": 1,
                "الإلكترونيات": 1,
                "الميكانيكا": 1,
                "الكهرباء": 1,
                "المدني": 1,
                "المعمارية": 1
            },
            "التاريخ": {
                "default": 2,
                "الحاسب الآلي": 2,
                "الإلكترونيات": 2,
                "الميكانيكا": 2,
                "الكهرباء": 2,
                "المدني": 2,
                "المعمارية": 3  # أكثر أهمية للمعمارية
            },
            "الأنشطة الطلابية": {
                "default": 1,
                "الحاسب الآلي": 1,
                "الإلكترونيات": 1,
                "الميكانيكا": 1,
                "الكهرباء": 1,
                "المدني": 1,
                "المعمارية": 1
            }
        }
        
        # 6. ربط كل مادة ثقافية بكل قسم مع الوحدات المناسبة
        print("🔗 ربط المواد الثقافية بجميع الأقسام...")
        total_links = 0
        
        for subject_id, subject_name in cultural_subjects:
            for dept_id, dept_name in departments:
                # تحديد عدد الوحدات
                subject_units = units_mapping.get(subject_name, {})
                units = subject_units.get(dept_name, subject_units.get("default", 2))
                
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO department_subjects 
                        (department_id, subject_id, units, is_required, semester, year_level)
                        VALUES (?, ?, ?, 1, 1, 1)
                    """, (dept_id, subject_id, units))
                    
                    total_links += 1
                    print(f"  ✅ {subject_name} → {dept_name} ({units} وحدات)")
                    
                except Exception as e:
                    print(f"  ❌ خطأ في ربط {subject_name} بـ {dept_name}: {e}")
        
        db_manager.connection.commit()
        
        # 7. عرض النتائج
        print(f"\n" + "=" * 60)
        print("📊 النتائج النهائية:")
        print("-" * 40)
        print(f"• عدد الأقسام: {len(departments)}")
        print(f"• عدد المواد الثقافية: {len(cultural_subjects)}")
        print(f"• إجمالي الروابط المُنشأة: {total_links}")
        
        # 8. عرض تفاصيل الروابط
        print(f"\n📋 تفاصيل الروابط:")
        print("-" * 60)
        
        cursor.execute("""
            SELECT d.name as dept_name, s.name as subject_name, ds.units
            FROM department_subjects ds
            JOIN departments d ON ds.department_id = d.id
            JOIN subjects s ON ds.subject_id = s.id
            WHERE s.is_cultural = 1
            ORDER BY d.name, s.name
        """)
        
        links = cursor.fetchall()
        current_dept = None
        
        for dept_name, subject_name, units in links:
            if dept_name != current_dept:
                print(f"\n🏢 {dept_name}:")
                current_dept = dept_name
            print(f"   📚 {subject_name}: {units} وحدات")
        
        print(f"\n✅ تم إصلاح نظام المواد الثقافية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح النظام: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

def test_add_cultural_subject():
    """اختبار إضافة مادة ثقافية جديدة"""
    print(f"\n🧪 اختبار إضافة مادة ثقافية جديدة")
    print("-" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # إضافة مادة ثقافية جديدة
        cursor.execute("""
            INSERT OR IGNORE INTO subjects 
            (name, code, department_id, subject_category, is_cultural, include_in_total, total_degree, description)
            VALUES (?, ?, NULL, ?, ?, ?, ?, ?)
        """, (
            "الجغرافيا",
            "GE101", 
            "ثقافية",
            1,
            1,
            100,
            "مادة الجغرافيا العامة"
        ))
        
        # الحصول على معرف المادة الجديدة
        cursor.execute("SELECT id FROM subjects WHERE name = 'الجغرافيا'")
        subject_result = cursor.fetchone()
        
        if subject_result:
            subject_id = subject_result[0]
            
            # ربطها بجميع الأقسام
            cursor.execute("SELECT id, name FROM departments")
            departments = cursor.fetchall()
            
            for dept_id, dept_name in departments:
                units = 2  # وحدتان افتراضياً
                cursor.execute("""
                    INSERT OR REPLACE INTO department_subjects 
                    (department_id, subject_id, units, is_required, semester, year_level)
                    VALUES (?, ?, ?, 1, 1, 1)
                """, (dept_id, subject_id, units))
            
            db_manager.connection.commit()
            print(f"✅ تم إضافة مادة الجغرافيا وربطها بجميع الأقسام!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    fix_cultural_subjects_system()
    test_add_cultural_subject()
