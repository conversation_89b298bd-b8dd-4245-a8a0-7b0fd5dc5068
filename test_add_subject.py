#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة مادة جديدة
Test adding a new subject
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_add_subject():
    """اختبار إضافة مادة جديدة"""
    
    print("🧪 اختبار إضافة مادة جديدة")
    print("=" * 50)
    
    # إنشاء اتصال بقاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # عرض المواد الموجودة حالياً
    cursor = db_manager.connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM subjects")
    current_count = cursor.fetchone()[0]
    print(f"📊 عدد المواد الحالية: {current_count}")
    
    # عرض المواد الموجودة
    cursor.execute("SELECT name, code, subject_category, is_cultural, include_in_total, units FROM subjects")
    existing_subjects = cursor.fetchall()
    
    print("\n📚 المواد الموجودة:")
    print("-" * 80)
    for subject in existing_subjects:
        name = subject[0]
        code = subject[1] if subject[1] else "بدون رمز"
        category = subject[2] if subject[2] else "غير محدد"
        cultural = "ثقافية" if subject[3] else "تخصصية"
        in_total = "نعم" if subject[4] else "لا"
        units = subject[5] if subject[5] else "غير محدد"
        print(f"• {name} ({code}) - {category} - {cultural} - يُضاف للمجموع: {in_total} - الوحدات: {units}")
    
    print("\n" + "=" * 50)
    
    # اختبار إضافة مادة ثقافية جديدة
    print("🔬 اختبار 1: إضافة مادة ثقافية جديدة")
    print("-" * 50)
    
    cultural_subject = {
        'name': 'التاريخ',
        'code': 'HI101',
        'department_id': 1,  # قسم افتراضي
        'subject_category': 'ثقافية',
        'is_cultural': 1,
        'include_in_total': 1,
        'units': 2,
        'total_degree': 100,
        'description': 'مادة التاريخ الأساسية للجميع'
    }
    
    success = add_subject_to_db(db_manager, cultural_subject)
    if success:
        print("✅ تم إضافة المادة الثقافية بنجاح!")
    else:
        print("❌ فشل في إضافة المادة الثقافية!")
    
    print("\n" + "=" * 50)
    
    # اختبار إضافة مادة تخصصية
    print("🔬 اختبار 2: إضافة مادة تخصصية")
    print("-" * 50)
    
    # الحصول على معرف قسم الحاسب الآلي
    cursor.execute("SELECT id FROM departments WHERE name LIKE '%Computer%' OR name LIKE '%حاسب%' LIMIT 1")
    dept_result = cursor.fetchone()
    dept_id = dept_result[0] if dept_result else 1
    
    specialized_subject = {
        'name': 'هياكل البيانات',
        'code': 'CS301',
        'department_id': dept_id,
        'subject_category': 'تخصصية',
        'is_cultural': 0,
        'include_in_total': 1,
        'units': 3,
        'total_degree': 100,
        'description': 'مادة هياكل البيانات والخوارزميات'
    }
    
    success = add_subject_to_db(db_manager, specialized_subject)
    if success:
        print("✅ تم إضافة المادة التخصصية بنجاح!")
    else:
        print("❌ فشل في إضافة المادة التخصصية!")
    
    print("\n" + "=" * 50)
    
    # اختبار إضافة مادة لا تُضاف للمجموع
    print("🔬 اختبار 3: إضافة مادة لا تُضاف للمجموع")
    print("-" * 50)
    
    non_total_subject = {
        'name': 'الأنشطة الطلابية',
        'code': 'AC101',
        'department_id': 1,
        'subject_category': 'ثقافية',
        'is_cultural': 1,
        'include_in_total': 0,  # لا تُضاف للمجموع
        'units': 1,
        'total_degree': 50,
        'description': 'مادة الأنشطة الطلابية - لا تُضاف للمجموع'
    }
    
    success = add_subject_to_db(db_manager, non_total_subject)
    if success:
        print("✅ تم إضافة المادة (غير مُضافة للمجموع) بنجاح!")
    else:
        print("❌ فشل في إضافة المادة!")
    
    # عرض النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 النتائج النهائية:")
    print("-" * 50)
    
    cursor.execute("SELECT COUNT(*) FROM subjects")
    final_count = cursor.fetchone()[0]
    print(f"• عدد المواد قبل الاختبار: {current_count}")
    print(f"• عدد المواد بعد الاختبار: {final_count}")
    print(f"• المواد المُضافة: {final_count - current_count}")
    
    # عرض جميع المواد
    cursor.execute("""
        SELECT name, code, subject_category, is_cultural, include_in_total, units, total_degree 
        FROM subjects 
        ORDER BY is_cultural DESC, name
    """)
    all_subjects = cursor.fetchall()
    
    print(f"\n📚 جميع المواد ({len(all_subjects)}):")
    print("-" * 80)
    for subject in all_subjects:
        name = subject[0]
        code = subject[1] if subject[1] else "بدون رمز"
        category = subject[2] if subject[2] else "غير محدد"
        cultural = "ثقافية" if subject[3] else "تخصصية"
        in_total = "نعم" if subject[4] else "لا"
        units = subject[5] if subject[5] else "غير محدد"
        degree = subject[6] if subject[6] else "غير محدد"
        print(f"• {name} ({code}) - {cultural} - يُضاف للمجموع: {in_total} - {units} وحدة - {degree} درجة")
    
    db_manager.close()
    print(f"\n✅ تم الانتهاء من الاختبار بنجاح!")

def add_subject_to_db(db_manager, subject_data):
    """إضافة مادة إلى قاعدة البيانات"""
    try:
        cursor = db_manager.connection.cursor()
        
        # التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(subjects)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # بناء استعلام الإدراج
        available_fields = []
        values = []
        
        for field, value in subject_data.items():
            if field in columns and value is not None:
                available_fields.append(field)
                values.append(value)
        
        if not available_fields:
            print("❌ لا توجد حقول صالحة للإدراج")
            return False
        
        placeholders = ', '.join(['?' for _ in available_fields])
        fields_str = ', '.join(available_fields)
        query = f"INSERT INTO subjects ({fields_str}) VALUES ({placeholders})"
        
        cursor.execute(query, values)
        db_manager.connection.commit()
        
        print(f"✅ تم إضافة المادة: {subject_data['name']}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المادة: {e}")
        return False

if __name__ == "__main__":
    test_add_subject()
