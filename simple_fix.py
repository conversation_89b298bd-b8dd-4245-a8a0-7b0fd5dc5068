#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بسيط لنظام المواد الثقافية
Simple fix for cultural subjects system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def simple_fix():
    """إصلاح بسيط للنظام"""
    
    print("🔧 إصلاح بسيط لنظام المواد")
    print("=" * 40)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # 1. تحديث المواد الثقافية لتكون بدون قسم محدد
        print("📝 تحديث المواد الثقافية...")
        cursor.execute("""
            UPDATE subjects 
            SET department_id = NULL 
            WHERE is_cultural = 1
        """)
        
        # 2. عرض المواد الثقافية
        cursor.execute("SELECT id, name FROM subjects WHERE is_cultural = 1")
        cultural_subjects = cursor.fetchall()
        print(f"📚 المواد الثقافية ({len(cultural_subjects)}):")
        for subject_id, name in cultural_subjects:
            print(f"  - {name}")
        
        # 3. عرض الأقسام
        cursor.execute("SELECT id, name FROM departments")
        departments = cursor.fetchall()
        print(f"\n🏢 الأقسام ({len(departments)}):")
        for dept_id, name in departments:
            print(f"  - {name}")
        
        db_manager.connection.commit()
        print(f"\n✅ تم الإصلاح بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

def test_add_new_subject():
    """اختبار إضافة مادة جديدة"""
    print(f"\n🧪 اختبار إضافة مادة جديدة")
    print("-" * 40)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # اختبار إضافة مادة ثقافية
        print("📝 إضافة مادة ثقافية جديدة...")
        cursor.execute("""
            INSERT OR IGNORE INTO subjects 
            (name, code, department_id, subject_category, is_cultural, include_in_total, total_degree, description)
            VALUES (?, ?, NULL, ?, ?, ?, ?, ?)
        """, (
            "علوم الحاسوب",
            "CS100", 
            "ثقافية",
            1,
            1,
            100,
            "مقدمة في علوم الحاسوب"
        ))
        
        # اختبار إضافة مادة تخصصية
        print("📝 إضافة مادة تخصصية جديدة...")
        cursor.execute("""
            INSERT OR IGNORE INTO subjects 
            (name, code, department_id, subject_category, is_cultural, include_in_total, total_degree, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "شبكات الحاسوب",
            "CS400", 
            1,  # قسم الحاسب الآلي
            "تخصصية",
            0,
            1,
            100,
            "مادة شبكات الحاسوب المتقدمة"
        ))
        
        db_manager.connection.commit()
        
        # عرض النتائج
        cursor.execute("SELECT COUNT(*) FROM subjects")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM subjects WHERE is_cultural = 1")
        cultural_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM subjects WHERE is_cultural = 0")
        specialized_count = cursor.fetchone()[0]
        
        print(f"\n📊 إحصائيات المواد:")
        print(f"  • إجمالي المواد: {total_count}")
        print(f"  • المواد الثقافية: {cultural_count}")
        print(f"  • المواد التخصصية: {specialized_count}")
        
        print(f"\n✅ تم الاختبار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    simple_fix()
    test_add_new_subject()
