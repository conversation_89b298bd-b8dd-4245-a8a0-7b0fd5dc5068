#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح المواد الثقافية في قاعدة البيانات المتقدمة
Fix cultural subjects in advanced database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.advanced_db_manager import AdvancedDatabaseManager

def fix_cultural_subjects_advanced():
    """إصلاح المواد الثقافية في قاعدة البيانات المتقدمة"""
    
    print("🔧 إصلاح المواد الثقافية في قاعدة البيانات المتقدمة")
    print("=" * 60)
    
    # الاتصال بقاعدة البيانات المتقدمة
    advanced_db = AdvancedDatabaseManager()
    advanced_db.initialize_database()
    
    try:
        # 1. إضافة المواد الثقافية مع department_id = 1 (أول قسم)
        print("📖 إضافة المواد الثقافية...")
        
        cultural_subjects = [
            ("اللغة العربية", "ARAB101", "ثقافية", 1, 1, 3, 100, "مادة اللغة العربية الأساسية"),
            ("اللغة الإنجليزية", "ENG101", "ثقافية", 1, 1, 3, 100, "مادة اللغة الإنجليزية الأساسية"),
            ("الرياضيات", "MATH101", "ثقافية", 1, 1, 4, 100, "مادة الرياضيات الأساسية"),
            ("الفيزياء", "PHYS101", "ثقافية", 1, 1, 3, 100, "مادة الفيزياء الأساسية"),
            ("الكيمياء", "CHEM101", "ثقافية", 1, 1, 3, 100, "مادة الكيمياء الأساسية"),
            ("التربية الإسلامية", "ISLAM101", "ثقافية", 1, 1, 2, 100, "مادة التربية الإسلامية"),
            ("التاريخ", "HIST101", "ثقافية", 1, 1, 2, 100, "مادة التاريخ"),
            ("الجغرافيا", "GEO101", "ثقافية", 1, 1, 2, 100, "مادة الجغرافيا"),
            ("الفلسفة", "PHIL101", "ثقافية", 1, 1, 2, 100, "مادة الفلسفة"),
            ("علم النفس", "PSYC101", "ثقافية", 1, 1, 2, 100, "مادة علم النفس"),
            ("الاقتصاد", "ECON101", "ثقافية", 1, 1, 2, 100, "مادة الاقتصاد"),
            ("الإحصاء", "STAT101", "ثقافية", 1, 1, 3, 100, "مادة الإحصاء"),
            ("الحاسوب الأساسي", "COMP101", "ثقافية", 1, 1, 3, 100, "مادة الحاسوب الأساسية")
        ]
        
        for subject_data in cultural_subjects:
            name, code, category, is_cultural, include_in_total, units, total_degree, description = subject_data
            
            try:
                # البحث عن المادة أولاً
                existing = advanced_db.execute_query(
                    "SELECT id FROM subjects WHERE name = ?", (name,)
                )
                
                if existing:
                    # تحديث المادة الموجودة
                    advanced_db.execute_update("""
                        UPDATE subjects SET 
                        code = ?, department_id = 1, subject_category = ?, 
                        is_cultural = ?, include_in_total = ?, units = ?, 
                        total_degree = ?, description = ?
                        WHERE name = ?
                    """, (code, category, is_cultural, include_in_total, units, total_degree, description, name))
                    print(f"  ✅ تم تحديث المادة: {name}")
                else:
                    # إضافة مادة جديدة
                    advanced_db.execute_update("""
                        INSERT INTO subjects 
                        (name, code, department_id, subject_category, is_cultural, include_in_total, units, total_degree, description) 
                        VALUES (?, ?, 1, ?, ?, ?, ?, ?, ?)
                    """, (name, code, category, is_cultural, include_in_total, units, total_degree, description))
                    print(f"  ✅ تم إضافة المادة: {name}")
                
            except Exception as e:
                print(f"  ❌ خطأ في معالجة المادة {name}: {e}")
        
        # 2. ربط المواد الثقافية بجميع الأقسام
        print(f"\n🔗 ربط المواد الثقافية بجميع الأقسام...")
        
        # الحصول على جميع الأقسام
        departments = advanced_db.execute_query("SELECT id FROM departments")
        cultural_subjects_db = advanced_db.execute_query(
            "SELECT id FROM subjects WHERE is_cultural = 1"
        )
        
        for dept in departments:
            dept_id = dept['id']
            for subject in cultural_subjects_db:
                subject_id = subject['id']
                
                try:
                    # التحقق من وجود الربط
                    existing_link = advanced_db.execute_query(
                        "SELECT 1 FROM department_subjects WHERE department_id = ? AND subject_id = ?",
                        (dept_id, subject_id)
                    )
                    
                    if not existing_link:
                        # إضافة الربط
                        advanced_db.execute_update(
                            "INSERT INTO department_subjects (department_id, subject_id, units) VALUES (?, ?, 3)",
                            (dept_id, subject_id)
                        )
                
                except Exception as e:
                    print(f"  ⚠️ خطأ في ربط المادة {subject_id} بالقسم {dept_id}: {e}")
        
        # 3. التحقق من النتائج
        print(f"\n📊 التحقق من النتائج:")
        
        # عدد المواد
        subjects_result = advanced_db.execute_query("SELECT COUNT(*) FROM subjects")
        subjects_count = subjects_result[0]['COUNT(*)'] if subjects_result else 0
        print(f"  📖 إجمالي المواد: {subjects_count}")
        
        # عدد المواد الثقافية
        cultural_result = advanced_db.execute_query("SELECT COUNT(*) FROM subjects WHERE is_cultural = 1")
        cultural_count = cultural_result[0]['COUNT(*)'] if cultural_result else 0
        print(f"  🌍 المواد الثقافية: {cultural_count}")
        
        # عدد الروابط
        links_result = advanced_db.execute_query("SELECT COUNT(*) FROM department_subjects")
        links_count = links_result[0]['COUNT(*)'] if links_result else 0
        print(f"  🔗 روابط المواد بالأقسام: {links_count}")
        
        # اختبار دالة get_subjects
        print(f"\n🧪 اختبار دالة get_subjects:")
        try:
            subjects = advanced_db.get_subjects()
            print(f"  ✅ get_subjects(): {len(subjects)} مادة")
            
            if subjects:
                print("  📝 عينة من المواد:")
                for i, subject in enumerate(subjects[:5]):
                    print(f"    {i+1}. {subject.get('name', 'بدون اسم')} | {subject.get('code', 'بدون رمز')} | {'ثقافية' if subject.get('is_cultural') else 'تخصصية'}")
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار get_subjects: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n" + "=" * 60)
        print("✅ تم إصلاح المواد الثقافية في قاعدة البيانات المتقدمة بنجاح!")
        print("🎯 يمكنك الآن تشغيل التطبيق الرئيسي وستجد المواد تعمل بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ عام في إصلاح المواد الثقافية: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        advanced_db.close()

if __name__ == "__main__":
    fix_cultural_subjects_advanced()
