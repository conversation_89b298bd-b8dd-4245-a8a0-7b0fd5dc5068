"""
Database Manager - Handles all database operations for the School Management System
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

class DatabaseManager:
    def __init__(self, db_path: str = "school_management.db"):
        """Initialize database manager with database path"""
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Enable column access by name
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False
            
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """Execute a SELECT query and return results"""
        if not self.connection:
            self.connect()
            
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except sqlite3.Error as e:
            print(f"Query execution error: {e}")
            return []
            
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT, UPDATE, or DELETE query and return affected rows"""
        if not self.connection:
            self.connect()
            
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            self.last_insert_id = cursor.lastrowid
            return cursor.rowcount
        except sqlite3.Error as e:
            print(f"Update execution error: {e}")
            self.connection.rollback()
            return 0
            
    def get_last_insert_id(self) -> int:
        """Get the last inserted row ID"""
        return getattr(self, 'last_insert_id', 0)
        
    def initialize_database(self):
        """Initialize database with required tables"""
        if not self.connect():
            raise Exception("Failed to connect to database")
            
        # Create tables
        self.create_tables()
        
        # Insert default data if tables are empty
        self.insert_default_data()
        
    def create_tables(self):
        """Create all required tables"""
        
        # Departments table
        departments_table = """
        CREATE TABLE IF NOT EXISTS departments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            code TEXT NOT NULL UNIQUE,
            head_of_department TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # Sections table
        sections_table = """
        CREATE TABLE IF NOT EXISTS sections (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            department_id INTEGER NOT NULL,
            capacity INTEGER DEFAULT 30,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE CASCADE,
            UNIQUE(name, department_id)
        )
        """
        
        # Subjects table - محسن لدعم المواد الثقافية والتخصصية
        subjects_table = """
        CREATE TABLE IF NOT EXISTS subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE,
            department_id INTEGER,
            subject_category TEXT DEFAULT 'ثقافية',  -- 'ثقافية' أو 'تخصصية'
            is_cultural BOOLEAN DEFAULT 1,  -- 1 للمواد الثقافية، 0 للتخصصية
            include_in_total BOOLEAN DEFAULT 1,  -- 1 يُضاف للمجموع، 0 لا يُضاف
            units INTEGER DEFAULT 1,  -- عدد الوحدات
            year_work_1 INTEGER DEFAULT 0,
            term_1 INTEGER DEFAULT 0,
            year_work_2 INTEGER DEFAULT 0,
            term_2 INTEGER DEFAULT 0,
            total_degree INTEGER DEFAULT 100,  -- إجمالي الدرجة
            description TEXT,  -- وصف المادة
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE SET NULL
        )
        """

        # Department Subjects table - ربط المواد بالتخصصات
        department_subjects_table = """
        CREATE TABLE IF NOT EXISTS department_subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            department_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            units INTEGER DEFAULT 1,  -- عدد الوحدات لهذه المادة في هذا التخصص
            is_required BOOLEAN DEFAULT 1,  -- مادة إجبارية أم اختيارية
            semester INTEGER DEFAULT 1,  -- الفصل الدراسي (1 أو 2)
            year_level INTEGER DEFAULT 1,  -- السنة الدراسية
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE,
            UNIQUE(department_id, subject_id)
        )
        """

        # Students table - Updated for Egyptian National ID system
        students_table = """
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id TEXT,
            first_name TEXT NOT NULL,
            last_name TEXT,
            national_id TEXT NOT NULL UNIQUE,
            seat_number TEXT,
            password TEXT,
            birth_date DATE,
            gender TEXT,
            governorate TEXT,
            age TEXT,
            email TEXT,
            phone TEXT,
            address TEXT,
            date_of_birth DATE,
            section_id INTEGER,
            department_id INTEGER,
            enrollment_date DATE,
            status TEXT DEFAULT 'نشط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL,
            FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE SET NULL
        )
        """
        
        # Add new columns to existing students table if they don't exist
        alter_students_queries = [
            "ALTER TABLE students ADD COLUMN national_id TEXT",
            "ALTER TABLE students ADD COLUMN seat_number TEXT", 
            "ALTER TABLE students ADD COLUMN password TEXT",
            "ALTER TABLE students ADD COLUMN birth_date DATE",
            "ALTER TABLE students ADD COLUMN governorate TEXT",
            "ALTER TABLE students ADD COLUMN age TEXT",
            "ALTER TABLE students ADD COLUMN department_id INTEGER"
        ]
        
        # Classes table
        classes_table = """
        CREATE TABLE IF NOT EXISTS classes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            section_id INTEGER NOT NULL,
            subject_id INTEGER NOT NULL,
            teacher_name TEXT,
            schedule TEXT,
            room_number TEXT,
            semester TEXT,
            academic_year TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE
        )
        """
        
        # Enrollments table (many-to-many relationship between students and classes)
        enrollments_table = """
        CREATE TABLE IF NOT EXISTS enrollments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            class_id INTEGER NOT NULL,
            enrollment_date DATE DEFAULT CURRENT_DATE,
            status TEXT DEFAULT 'Active',
            FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
            FOREIGN KEY (class_id) REFERENCES classes (id) ON DELETE CASCADE,
            UNIQUE(student_id, class_id)
        )
        """
        
        # Exams table
        exams_table = """
        CREATE TABLE IF NOT EXISTS exams (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            class_id INTEGER NOT NULL,
            exam_date DATE,
            exam_type TEXT,
            total_marks INTEGER DEFAULT 100,
            duration_minutes INTEGER DEFAULT 120,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes (id) ON DELETE CASCADE
        )
        """
        
        # Exam Results table
        exam_results_table = """
        CREATE TABLE IF NOT EXISTS exam_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            exam_id INTEGER NOT NULL,
            student_id INTEGER NOT NULL,
            marks_obtained REAL NOT NULL,
            grade TEXT,
            remarks TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exam_id) REFERENCES exams (id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
            UNIQUE(exam_id, student_id)
        )
        """
        
        # Execute table creation queries
        tables = [
            departments_table,
            sections_table,
            subjects_table,
            department_subjects_table,
            students_table,
            classes_table,
            enrollments_table,
            exams_table,
            exam_results_table
        ]
        
        for table_query in tables:
            self.execute_update(table_query)
            
        # Add new columns to existing students table if they don't exist
        try:
            cursor = self.connection.cursor()
            
            # Check existing columns for students table
            cursor.execute("PRAGMA table_info(students)")
            existing_columns = [column[1] for column in cursor.fetchall()]
            
            # Add new columns if they don't exist
            new_columns = {
                'national_id': 'ALTER TABLE students ADD COLUMN national_id TEXT',
                'seat_number': 'ALTER TABLE students ADD COLUMN seat_number TEXT',
                'password': 'ALTER TABLE students ADD COLUMN password TEXT',
                'birth_date': 'ALTER TABLE students ADD COLUMN birth_date DATE',
                'governorate': 'ALTER TABLE students ADD COLUMN governorate TEXT',
                'age': 'ALTER TABLE students ADD COLUMN age TEXT',
                'department_id': 'ALTER TABLE students ADD COLUMN department_id INTEGER'
            }
            
            for column_name, alter_query in new_columns.items():
                if column_name not in existing_columns:
                    try:
                        cursor.execute(alter_query)
                        self.connection.commit()
                        print(f"Added column: {column_name}")
                    except sqlite3.Error as e:
                        print(f"Error adding column {column_name}: {e}")
                        
        except sqlite3.Error as e:
            print(f"Error updating students table: {e}")
            
        # Add new columns to existing subjects table if they don't exist
        try:
            cursor = self.connection.cursor()
            
            # Check existing columns for subjects table
            cursor.execute("PRAGMA table_info(subjects)")
            existing_columns = [column[1] for column in cursor.fetchall()]
            
            # Add new columns if they don't exist
            subjects_new_columns = {
                'code': 'ALTER TABLE subjects ADD COLUMN code TEXT',
                'subject_category': 'ALTER TABLE subjects ADD COLUMN subject_category TEXT DEFAULT "ثقافية"',
                'is_cultural': 'ALTER TABLE subjects ADD COLUMN is_cultural BOOLEAN DEFAULT 1',
                'include_in_total': 'ALTER TABLE subjects ADD COLUMN include_in_total BOOLEAN DEFAULT 1',
                'units': 'ALTER TABLE subjects ADD COLUMN units INTEGER DEFAULT 1',
                'total_degree': 'ALTER TABLE subjects ADD COLUMN total_degree INTEGER DEFAULT 100',
                'description': 'ALTER TABLE subjects ADD COLUMN description TEXT',
                'type': 'ALTER TABLE subjects ADD COLUMN type TEXT DEFAULT "ثقافية"',
                'year_work_1': 'ALTER TABLE subjects ADD COLUMN year_work_1 INTEGER DEFAULT 0',
                'term_1': 'ALTER TABLE subjects ADD COLUMN term_1 INTEGER DEFAULT 0',
                'year_work_2': 'ALTER TABLE subjects ADD COLUMN year_work_2 INTEGER DEFAULT 0',
                'term_2': 'ALTER TABLE subjects ADD COLUMN term_2 INTEGER DEFAULT 0'
            }
            
            for column_name, alter_query in subjects_new_columns.items():
                if column_name not in existing_columns:
                    try:
                        cursor.execute(alter_query)
                        self.connection.commit()
                        print(f"Added column to subjects: {column_name}")
                    except sqlite3.Error as e:
                        print(f"Error adding column {column_name} to subjects: {e}")
                        
        except sqlite3.Error as e:
            print(f"Error updating subjects table: {e}")
            
    def insert_default_data(self):
        """Insert default data if tables are empty"""
        # Check if departments table is empty
        departments = self.execute_query("SELECT COUNT(*) as count FROM departments")
        if departments[0]['count'] == 0:
            # Insert default departments
            default_departments = [
                ("Computer Science", "CS", "Dr. John Smith", "Department of Computer Science and Information Technology"),
                ("Electrical Engineering", "EE", "Dr. Sarah Johnson", "Department of Electrical and Electronics Engineering"),
                ("Mechanical Engineering", "ME", "Dr. Michael Brown", "Department of Mechanical Engineering"),
                ("Civil Engineering", "CE", "Dr. Emily Davis", "Department of Civil Engineering"),
                ("Information Technology", "IT", "Dr. Robert Wilson", "Department of Information Technology")
            ]
            
            for dept in default_departments:
                # إذا كنت تستخدم AdvancedDatabaseManager، مرر فقط الاسم
                try:
                    self.add_department(dept[0])
                except TypeError:
                    # إذا كانت الدالة القديمة مطلوبة، مرر جميع الوسائط
                    self.add_department(dept[0], dept[1], dept[2], dept[3])

        # إضافة المواد الثقافية الافتراضية
        subjects = self.execute_query("SELECT COUNT(*) as count FROM subjects")
        if subjects[0]['count'] == 0:
            self.add_default_cultural_subjects()

    def add_default_cultural_subjects(self):
        """إضافة المواد الثقافية الافتراضية"""
        try:
            # الحصول على معرف قسم افتراضي للمواد الثقافية
            cursor = self.connection.cursor()
            cursor.execute("SELECT id FROM departments LIMIT 1")
            default_dept = cursor.fetchone()
            default_dept_id = default_dept[0] if default_dept else 1

            # المواد الثقافية الافتراضية
            cultural_subjects = [
                ("اللغة العربية", "AR101", default_dept_id, "ثقافية", 1, 1, 2, 100, "مادة اللغة العربية الأساسية"),
                ("اللغة الإنجليزية", "EN101", default_dept_id, "ثقافية", 1, 1, 2, 100, "مادة اللغة الإنجليزية الأساسية"),
                ("الرياضيات", "MA101", default_dept_id, "ثقافية", 1, 1, 3, 100, "مادة الرياضيات الأساسية"),
                ("الفيزياء", "PH101", default_dept_id, "ثقافية", 1, 1, 2, 100, "مادة الفيزياء الأساسية"),
                ("الكيمياء", "CH101", default_dept_id, "ثقافية", 1, 1, 2, 100, "مادة الكيمياء الأساسية"),
                ("التربية الدينية", "RE101", default_dept_id, "ثقافية", 1, 0, 1, 50, "مادة التربية الدينية - لا تُضاف للمجموع"),
                ("التربية الرياضية", "PE101", default_dept_id, "ثقافية", 1, 0, 1, 50, "مادة التربية الرياضية - لا تُضاف للمجموع")
            ]

            # التحقق من الأعمدة الموجودة
            cursor = self.connection.cursor()
            cursor.execute("PRAGMA table_info(subjects)")
            columns = [column[1] for column in cursor.fetchall()]

            for subject in cultural_subjects:
                try:
                    if 'subject_category' in columns:
                        query = """
                            INSERT INTO subjects (name, code, department_id, subject_category, is_cultural, include_in_total, units, total_degree, description)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        self.execute_update(query, subject)
                    else:
                        # للتوافق مع النظام القديم
                        query = "INSERT INTO subjects (name, type) VALUES (?, ?)"
                        self.execute_update(query, (subject[0], subject[3]))
                except Exception as e:
                    print(f"خطأ في إضافة المادة {subject[0]}: {e}")

            print("تم إدراج المواد الثقافية الافتراضية")

        except Exception as e:
            print(f"خطأ في إضافة المواد الثقافية الافتراضية: {e}")

    # Department operations
    def add_department(self, name: str, code: str, head: str = "", description: str = "") -> int:
        """Add a new department"""
        query = "INSERT INTO departments (name, code, head_of_department, description) VALUES (?, ?, ?, ?)"
        self.execute_update(query, (name, code, head, description))
        return self.get_last_insert_id()
        
    def update_department(self, dept_id: int, name: str, code: str, head: str = "", description: str = "") -> bool:
        """Update an existing department"""
        query = "UPDATE departments SET name=?, code=?, head_of_department=?, description=? WHERE id=?"
        return self.execute_update(query, (name, code, head, description, dept_id)) > 0
        
    def delete_department(self, dept_id: int) -> bool:
        """Delete a department"""
        query = "DELETE FROM departments WHERE id=?"
        return self.execute_update(query, (dept_id,)) > 0
        
    def get_departments(self) -> List[Dict]:
        """Get all departments"""
        query = "SELECT * FROM departments ORDER BY name"
        return self.execute_query(query)
        
    # Section operations
    def add_section(self, name: str, department_id: int, capacity: int = 30) -> int:
        """Add a new section"""
        query = "INSERT INTO sections (name, department_id, capacity) VALUES (?, ?, ?)"
        self.execute_update(query, (name, department_id, capacity))
        return self.get_last_insert_id()
        
    def get_sections(self, department_id: int = None) -> List[Dict]:
        """Get sections, optionally filtered by department"""
        if department_id:
            query = """
                SELECT s.*, d.name as department_name 
                FROM sections s 
                JOIN departments d ON s.department_id = d.id 
                WHERE s.department_id = ?
                ORDER BY s.name
            """
            return self.execute_query(query, (department_id,))
        else:
            query = """
                SELECT s.*, d.name as department_name 
                FROM sections s 
                JOIN departments d ON s.department_id = d.id 
                ORDER BY d.name, s.name
            """
            return self.execute_query(query)
            
    # Subject operations
    def add_subject(self, name: str, department_id: int, type: str = "ثقافية", year_work_1: int = 0, term_1: int = 0, year_work_2: int = 0, term_2: int = 0) -> int:
        """Add a new subject (دون كود أو وصف أو credits) مع معالجة القيم None وتحسين رسائل الخطأ"""
        # معالجة القيم None
        if name is None or name.strip() == "":
            print("[AddSubject] فشل: اسم المادة فارغ")
            return 0
        if department_id is None:
            print(f"[AddSubject] فشل: رقم القسم غير محدد للمادة {name}")
            return 0
        # تأكد من أن القيم الرقمية ليست None
        year_work_1 = year_work_1 if year_work_1 is not None else 0
        term_1 = term_1 if term_1 is not None else 0
        year_work_2 = year_work_2 if year_work_2 is not None else 0
        term_2 = term_2 if term_2 is not None else 0
        try:
            query = "INSERT INTO subjects (name, department_id, type, year_work_1, term_1, year_work_2, term_2) VALUES (?, ?, ?, ?, ?, ?, ?)"
            result = self.execute_update(query, (name, department_id, type, year_work_1, term_1, year_work_2, term_2))
            if result == 0:
                print(f"[AddSubject] فشل في إضافة المادة! تحقق من أن اسم المادة غير مكرر أو أن هناك مشكلة في البيانات.")
                return 0
            return self.get_last_insert_id()
        except Exception as e:
            print(f"[AddSubject] خطأ أثناء إضافة المادة: {e} | البيانات: name={name}, department_id={department_id}, type={type}, year_work_1={year_work_1}, term_1={term_1}, year_work_2={year_work_2}, term_2={term_2}")
            return 0
        
    def update_subject(self, subject_id: int, name: str, department_id: int, type: str = "ثقافية", year_work_1: int = 0, term_1: int = 0, year_work_2: int = 0, term_2: int = 0) -> bool:
        """Update an existing subject (دون كود أو وصف أو credits) مع معالجة القيم None وتحسين رسائل الخطأ"""
        if name is None or name.strip() == "":
            print("[UpdateSubject] فشل: اسم المادة فارغ")
            return False
        if department_id is None:
            print(f"[UpdateSubject] فشل: رقم القسم غير محدد للمادة {name}")
            return False
        year_work_1 = year_work_1 if year_work_1 is not None else 0
        term_1 = term_1 if term_1 is not None else 0
        year_work_2 = year_work_2 if year_work_2 is not None else 0
        term_2 = term_2 if term_2 is not None else 0
        try:
            query = "UPDATE subjects SET name = ?, department_id = ?, type = ?, year_work_1 = ?, term_1 = ?, year_work_2 = ?, term_2 = ? WHERE id = ?"
            result = self.execute_update(query, (name, department_id, type, year_work_1, term_1, year_work_2, term_2, subject_id)) > 0
            return result
        except Exception as e:
            print(f"[UpdateSubject] خطأ أثناء تعديل المادة: {e} | البيانات: id={subject_id}, name={name}, department_id={department_id}, type={type}, year_work_1={year_work_1}, term_1={term_1}, year_work_2={year_work_2}, term_2={term_2}")
            return False
        
    def get_subjects(self, department_id: int = None) -> List[Dict]:
        """Get subjects, optionally filtered by department"""
        if department_id:
            query = """
                SELECT s.id, s.name, s.department_id, s.type, s.year_work_1, s.term_1, s.year_work_2, s.term_2, d.name as department_name
                FROM subjects s 
                JOIN departments d ON s.department_id = d.id 
                WHERE s.department_id = ?
                ORDER BY s.name
            """
            return self.execute_query(query, (department_id,))
        else:
            query = """
                SELECT s.id, s.name, s.department_id, s.type, s.year_work_1, s.term_1, s.year_work_2, s.term_2, d.name as department_name
                FROM subjects s 
                JOIN departments d ON s.department_id = d.id 
                ORDER BY d.name, s.name
            """
            return self.execute_query(query)
            
    # Student operations
    def add_student(self, student_id: str, first_name: str, last_name: str, email: str = "", 
                   phone: str = "", address: str = "", date_of_birth: str = "", gender: str = "",
                   section_id: int = None, enrollment_date: str = "") -> int:
        """Add a new student"""
        if not enrollment_date:
            enrollment_date = datetime.now().strftime('%Y-%m-%d')
            
        query = """
            INSERT INTO students (student_id, first_name, last_name, email, phone, address, 
                                date_of_birth, gender, section_id, enrollment_date) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        self.execute_update(query, (student_id, first_name, last_name, email, phone, address,
                                  date_of_birth, gender, section_id, enrollment_date))
        
        student_db_id = self.get_last_insert_id()
        
        # Auto-enroll student in classes of their section
        if section_id:
            self.auto_enroll_student(student_db_id, section_id)
            
        return student_db_id
    
    def add_student_new(self, student_data: dict) -> int:
        """Add a new student with updated fields for Egyptian National ID system"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        query = """
            INSERT INTO students (
                first_name, national_id, seat_number, password, birth_date, 
                gender, governorate, age, phone, department_id, address, 
                enrollment_date, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            student_data.get('first_name', ''),
            student_data.get('national_id', ''),
            student_data.get('seat_number', ''),
            student_data.get('password', ''),
            student_data.get('birth_date', ''),
            student_data.get('gender', ''),
            student_data.get('governorate', ''),
            student_data.get('age', ''),
            student_data.get('phone', ''),
            student_data.get('department_id'),
            student_data.get('address', ''),
            current_date,
            'نشط'
        )
        
        self.execute_update(query, params)
        return self.get_last_insert_id()
        
    def auto_enroll_student(self, student_id: int, section_id: int):
        """Automatically enroll student in all classes of their section"""
        # Get all classes for the section
        classes = self.execute_query("SELECT id FROM classes WHERE section_id = ?", (section_id,))
        
        # Enroll student in each class
        for cls in classes:
            try:
                self.execute_update(
                    "INSERT OR IGNORE INTO enrollments (student_id, class_id) VALUES (?, ?)",
                    (student_id, cls['id'])
                )
            except:
                pass  # Ignore if already enrolled
                
    def get_students(self) -> List[Dict]:
        """Get all students with their section and department information"""
        query = """
            SELECT s.*, sec.name as section_name, d.name as department_name
            FROM students s
            LEFT JOIN sections sec ON s.section_id = sec.id
            LEFT JOIN departments d ON sec.department_id = d.id
            ORDER BY s.last_name, s.first_name
        """
        return self.execute_query(query)
        
    # Class operations
    def add_class(self, name: str, section_id: int, subject_id: int, teacher_name: str = "",
                 schedule: str = "", room_number: str = "", semester: str = "", academic_year: str = "") -> int:
        """Add a new class"""
        query = """
            INSERT INTO classes (name, section_id, subject_id, teacher_name, schedule, 
                               room_number, semester, academic_year) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        self.execute_update(query, (name, section_id, subject_id, teacher_name, schedule,
                                  room_number, semester, academic_year))
        
        class_id = self.get_last_insert_id()
        
        # Auto-enroll all students from the section
        self.auto_enroll_section_students(class_id, section_id)
        
        return class_id
        
    def auto_enroll_section_students(self, class_id: int, section_id: int):
        """Automatically enroll all students from a section into a class"""
        students = self.execute_query("SELECT id FROM students WHERE section_id = ?", (section_id,))
        
        for student in students:
            try:
                self.execute_update(
                    "INSERT OR IGNORE INTO enrollments (student_id, class_id) VALUES (?, ?)",
                    (student['id'], class_id)
                )
            except:
                pass  # Ignore if already enrolled
                
    def get_classes(self) -> List[Dict]:
        """Get all classes with related information"""
        query = """
            SELECT c.*, sec.name as section_name, sub.name as subject_name, sub.code as subject_code,
                   d.name as department_name
            FROM classes c
            JOIN sections sec ON c.section_id = sec.id
            JOIN subjects sub ON c.subject_id = sub.id
            JOIN departments d ON sec.department_id = d.id
            ORDER BY c.academic_year DESC, c.semester, sub.name
        """
        return self.execute_query(query)
        
    # Exam operations
    def add_exam(self, name: str, class_id: int, exam_date: str = "", exam_type: str = "",
                total_marks: int = 100, duration_minutes: int = 120, description: str = "") -> int:
        """Add a new exam"""
        query = """
            INSERT INTO exams (name, class_id, exam_date, exam_type, total_marks, 
                             duration_minutes, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        self.execute_update(query, (name, class_id, exam_date, exam_type, total_marks,
                                  duration_minutes, description))
        return self.get_last_insert_id()
        
    def get_exams(self) -> List[Dict]:
        """Get all exams with related information"""
        query = """
            SELECT e.*, c.name as class_name, sub.name as subject_name, sub.code as subject_code,
                   sec.name as section_name
            FROM exams e
            JOIN classes c ON e.class_id = c.id
            JOIN subjects sub ON c.subject_id = sub.id
            JOIN sections sec ON c.section_id = sec.id
            ORDER BY e.exam_date DESC, e.name
        """
        return self.execute_query(query)
        
    # Exam Results operations
    def add_exam_result(self, exam_id: int, student_id: int, marks_obtained: float,
                       grade: str = "", remarks: str = "") -> int:
        """Add or update an exam result"""
        # Check if result already exists
        existing = self.execute_query(
            "SELECT id FROM exam_results WHERE exam_id = ? AND student_id = ?",
            (exam_id, student_id)
        )
        
        if existing:
            # Update existing result
            query = """
                UPDATE exam_results 
                SET marks_obtained = ?, grade = ?, remarks = ? 
                WHERE exam_id = ? AND student_id = ?
            """
            self.execute_update(query, (marks_obtained, grade, remarks, exam_id, student_id))
            return existing[0]['id']
        else:
            # Insert new result
            query = """
                INSERT INTO exam_results (exam_id, student_id, marks_obtained, grade, remarks) 
                VALUES (?, ?, ?, ?, ?)
            """
            self.execute_update(query, (exam_id, student_id, marks_obtained, grade, remarks))
            return self.get_last_insert_id()
            
    def get_exam_results(self, exam_id: int = None) -> List[Dict]:
        """Get exam results, optionally filtered by exam"""
        if exam_id:
            query = """
                SELECT er.*, e.name as exam_name, e.total_marks, s.student_id, 
                       s.first_name, s.last_name, sub.name as subject_name
                FROM exam_results er
                JOIN exams e ON er.exam_id = e.id
                JOIN students s ON er.student_id = s.id
                JOIN classes c ON e.class_id = c.id
                JOIN subjects sub ON c.subject_id = sub.id
                WHERE er.exam_id = ?
                ORDER BY s.last_name, s.first_name
            """
            return self.execute_query(query, (exam_id,))
        else:
            query = """
                SELECT er.*, e.name as exam_name, e.total_marks, s.student_id, 
                       s.first_name, s.last_name, sub.name as subject_name
                FROM exam_results er
                JOIN exams e ON er.exam_id = e.id
                JOIN students s ON er.student_id = s.id
                JOIN classes c ON e.class_id = c.id
                JOIN subjects sub ON c.subject_id = sub.id
                ORDER BY e.exam_date DESC, s.last_name, s.first_name
            """
            return self.execute_query(query)
            
    def calculate_grade(self, marks_obtained: float, total_marks: int) -> str:
        """Calculate letter grade based on percentage"""
        if total_marks == 0:
            return "N/A"
            
        percentage = (marks_obtained / total_marks) * 100
        
        if percentage >= 90:
            return "A+"
        elif percentage >= 85:
            return "A"
        elif percentage >= 80:
            return "A-"
        elif percentage >= 75:
            return "B+"
        elif percentage >= 70:
            return "B"
        elif percentage >= 65:
            return "B-"
        elif percentage >= 60:
            return "C+"
        elif percentage >= 55:
            return "C"
        elif percentage >= 50:
            return "C-"
        elif percentage >= 45:
            return "D"
        else:
            return "F"
            
    def get_student_gpa(self, student_id: int) -> float:
        """Calculate GPA for a student (بدون الاعتماد على credits)"""
        # Get all exam results for the student
        results = self.execute_query("""
            SELECT er.marks_obtained, e.total_marks
            FROM exam_results er
            JOIN exams e ON er.exam_id = e.id
            WHERE er.student_id = ?
        """, (student_id,))
        if not results:
            return 0.0
        total_grade_points = 0
        for result in results:
            percentage = (result['marks_obtained'] / result['total_marks']) * 100
            grade_point = self.percentage_to_grade_point(percentage)
            total_grade_points += grade_point
        return total_grade_points / len(results) if results else 0.0
        
    def percentage_to_grade_point(self, percentage: float) -> float:
        """Convert percentage to grade point"""
        if percentage >= 90:
            return 4.0
        elif percentage >= 85:
            return 3.7
        elif percentage >= 80:
            return 3.3
        elif percentage >= 75:
            return 3.0
        elif percentage >= 70:
            return 2.7
        elif percentage >= 65:
            return 2.3
        elif percentage >= 60:
            return 2.0
        elif percentage >= 55:
            return 1.7
        elif percentage >= 50:
            return 1.3
        elif percentage >= 45:
            return 1.0
        else:
            return 0.0
            
    def get_subject_type(self, subject_id: int) -> str:
        """Get the type of a subject by its ID"""
        query = "SELECT type FROM subjects WHERE id = ?"
        result = self.execute_query(query, (subject_id,))
        if result and 'type' in result[0]:
            return result[0]['type']
        return "ثقافية"