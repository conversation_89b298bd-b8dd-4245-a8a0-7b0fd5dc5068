#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تبويب الفصول في التطبيق الرئيسي
Test classes tab in main application
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from gui.main_window import MainWindow

def test_main_app_classes():
    """اختبار تبويب الفصول في التطبيق الرئيسي"""
    
    print("🧪 اختبار تبويب الفصول في التطبيق الرئيسي")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق الرئيسي
        print("🏗️ إنشاء التطبيق الرئيسي...")
        app = MainWindow()
        print("  ✅ تم إنشاء التطبيق بنجاح")
        
        # إخفاء النافذة لتجنب التداخل
        app.root.withdraw()
        
        # اختبار الدخول لتبويب الفصول
        print("\n🎯 اختبار الدخول لتبويب الفصول...")
        
        try:
            app.show_section('classes')
            print("  ✅ تم الدخول لتبويب الفصول بنجاح")
            
            # التحقق من إنشاء التبويب
            if 'classes' in app.sections:
                classes_section = app.sections['classes']
                
                if classes_section['widget'] is not None:
                    print("  ✅ تم إنشاء واجهة الفصول بنجاح")
                    
                    # اختبار تحديث البيانات
                    print("\n🔄 اختبار تحديث البيانات...")
                    try:
                        classes_section['widget'].refresh_data()
                        print("  ✅ تم تحديث البيانات بنجاح")
                        
                        # فحص عدد الفصول المحملة
                        tree = classes_section['widget'].tree
                        items_count = len(tree.get_children())
                        print(f"  📊 عدد الفصول المحملة: {items_count}")
                        
                        if items_count > 0:
                            print("  ✅ تم تحميل الفصول بنجاح")
                            
                            # عرض عينة من الفصول
                            print("  📝 عينة من الفصول:")
                            for i, item in enumerate(tree.get_children()[:3]):
                                values = tree.item(item)['values']
                                print(f"    {i+1}. {values[1]} | {values[2]} | {values[3]}")
                        else:
                            print("  ⚠️ لا توجد فصول محملة")
                        
                    except Exception as e:
                        print(f"  ❌ خطأ في تحديث البيانات: {e}")
                        import traceback
                        traceback.print_exc()
                    
                    # اختبار تحميل الشعب والمواد
                    print("\n📚 اختبار تحميل الشعب والمواد...")
                    try:
                        classes_section['widget'].load_sections_and_subjects()
                        print("  ✅ تم تحميل الشعب والمواد بنجاح")
                        
                        # فحص القوائم المنسدلة
                        section_combo = classes_section['widget'].section_combo
                        subject_combo = classes_section['widget'].subject_combo
                        
                        sections_count = len(section_combo['values'])
                        subjects_count = len(subject_combo['values'])
                        
                        print(f"  📊 عدد الشعب: {sections_count}")
                        print(f"  📊 عدد المواد: {subjects_count}")
                        
                    except Exception as e:
                        print(f"  ❌ خطأ في تحميل الشعب والمواد: {e}")
                        import traceback
                        traceback.print_exc()
                    
                else:
                    print("  ❌ فشل في إنشاء واجهة الفصول")
            else:
                print("  ❌ تبويب الفصول غير موجود")
                
        except Exception as e:
            print(f"  ❌ خطأ في الدخول لتبويب الفصول: {e}")
            import traceback
            traceback.print_exc()
        
        # اختبار التنقل بين التبويبات
        print("\n🔄 اختبار التنقل بين التبويبات...")
        try:
            # الذهاب للوحة الرئيسية
            app.show_section('dashboard')
            print("  ✅ تم الانتقال للوحة الرئيسية")
            
            # العودة لتبويب الفصول
            app.show_section('classes')
            print("  ✅ تم العودة لتبويب الفصول")
            
        except Exception as e:
            print(f"  ❌ خطأ في التنقل: {e}")
            import traceback
            traceback.print_exc()
        
        # إغلاق التطبيق
        app.root.destroy()
        print("\n✅ تم إغلاق التطبيق بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ عام في التطبيق: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("✅ انتهى اختبار تبويب الفصول في التطبيق الرئيسي")

if __name__ == "__main__":
    test_main_app_classes()
