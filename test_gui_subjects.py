#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة المواد من خلال محاكاة الواجهة
Test adding subjects through GUI simulation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from gui.modern_subjects_tab import ModernSubjectsTab
import tkinter as tk

def test_gui_add_subject():
    """اختبار إضافة مادة من خلال محاكاة الواجهة"""
    
    print("🧪 اختبار إضافة المواد من خلال الواجهة")
    print("=" * 60)
    
    # إنشاء قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # عرض حالة قاعدة البيانات قبل الاختبار
    cursor = db_manager.connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM subjects")
    initial_count = cursor.fetchone()[0]
    print(f"📊 عدد المواد قبل الاختبار: {initial_count}")
    
    # إنشاء نافذة وهمية للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة
    
    try:
        # إنشاء تبويب المواد
        subjects_tab = ModernSubjectsTab(root, db_manager)
        
        print("\n🔬 اختبار 1: إضافة مادة ثقافية")
        print("-" * 40)
        
        # محاكاة بيانات المادة الثقافية
        cultural_subject_data = {
            'name': 'الجغرافيا',
            'code': 'GE101',
            'department_id': 1,
            'subject_category': 'ثقافية',
            'is_cultural': 1,
            'include_in_total': 1,
            'units': 2,
            'total_degree': 100,
            'year_work_1': 20,
            'term_1': 30,
            'year_work_2': 20,
            'term_2': 30,
            'description': 'مادة الجغرافيا الأساسية'
        }
        
        # اختبار دالة الحفظ
        success = subjects_tab.save_subject_to_db(cultural_subject_data)
        if success:
            print("✅ تم إضافة المادة الثقافية بنجاح!")
        else:
            print("❌ فشل في إضافة المادة الثقافية!")
        
        print("\n🔬 اختبار 2: إضافة مادة تخصصية")
        print("-" * 40)
        
        # محاكاة بيانات المادة التخصصية
        specialized_subject_data = {
            'name': 'قواعد البيانات',
            'code': 'CS401',
            'department_id': 1,
            'subject_category': 'تخصصية',
            'is_cultural': 0,
            'include_in_total': 1,
            'units': 3,
            'total_degree': 100,
            'year_work_1': 20,
            'term_1': 30,
            'year_work_2': 20,
            'term_2': 30,
            'description': 'مادة قواعد البيانات المتقدمة'
        }
        
        success = subjects_tab.save_subject_to_db(specialized_subject_data)
        if success:
            print("✅ تم إضافة المادة التخصصية بنجاح!")
        else:
            print("❌ فشل في إضافة المادة التخصصية!")
        
        print("\n🔬 اختبار 3: إضافة مادة لا تُضاف للمجموع")
        print("-" * 40)
        
        # محاكاة بيانات مادة لا تُضاف للمجموع
        non_total_subject_data = {
            'name': 'النشاط الثقافي',
            'code': 'CU101',
            'department_id': 1,
            'subject_category': 'ثقافية',
            'is_cultural': 1,
            'include_in_total': 0,  # لا تُضاف للمجموع
            'units': 1,
            'total_degree': 50,
            'year_work_1': 25,
            'term_1': 25,
            'year_work_2': 0,
            'term_2': 0,
            'description': 'مادة النشاط الثقافي - لا تُضاف للمجموع'
        }
        
        success = subjects_tab.save_subject_to_db(non_total_subject_data)
        if success:
            print("✅ تم إضافة المادة (غير مُضافة للمجموع) بنجاح!")
        else:
            print("❌ فشل في إضافة المادة!")
        
        # عرض النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 النتائج النهائية:")
        print("-" * 40)
        
        cursor.execute("SELECT COUNT(*) FROM subjects")
        final_count = cursor.fetchone()[0]
        added_count = final_count - initial_count
        
        print(f"• عدد المواد قبل الاختبار: {initial_count}")
        print(f"• عدد المواد بعد الاختبار: {final_count}")
        print(f"• المواد المُضافة: {added_count}")
        
        if added_count > 0:
            print(f"\n📚 المواد المُضافة حديثاً:")
            cursor.execute("""
                SELECT name, code, subject_category, is_cultural, include_in_total, units, total_degree 
                FROM subjects 
                ORDER BY id DESC 
                LIMIT ?
            """, (added_count,))
            
            new_subjects = cursor.fetchall()
            for i, subject in enumerate(new_subjects, 1):
                name = subject[0]
                code = subject[1] if subject[1] else "بدون رمز"
                category = subject[2] if subject[2] else "غير محدد"
                cultural = "ثقافية" if subject[3] else "تخصصية"
                in_total = "نعم" if subject[4] else "لا"
                units = subject[5] if subject[5] else "غير محدد"
                degree = subject[6] if subject[6] else "غير محدد"
                
                print(f"{i}. {name} ({code})")
                print(f"   - النوع: {cultural}")
                print(f"   - يُضاف للمجموع: {in_total}")
                print(f"   - الوحدات: {units}")
                print(f"   - الدرجة: {degree}")
                print()
        
        print("✅ تم الانتهاء من اختبار الواجهة بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # تنظيف الموارد
        root.destroy()
        db_manager.close()

if __name__ == "__main__":
    test_gui_add_subject()
