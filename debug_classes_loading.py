#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة تحميل الفصول في التطبيق الرئيسي
Debug classes loading in main application
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from database.db_manager import DatabaseManager
from gui.classes_tab import ClassesTab

def debug_classes_loading():
    """تشخيص مشكلة تحميل الفصول"""
    
    print("🔍 تشخيص مشكلة تحميل الفصول في التطبيق الرئيسي")
    print("=" * 60)
    
    # 1. اختبار قاعدة البيانات
    print("📊 اختبار قاعدة البيانات:")
    db_manager = DatabaseManager()
    
    try:
        db_manager.connect()
        print("  ✅ الاتصال بقاعدة البيانات نجح")
        
        # اختبار البيانات الأساسية
        cursor = db_manager.connection.cursor()
        
        # فحص الجداول
        tables_to_check = ['classes', 'sections', 'subjects', 'departments']
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  📋 {table}: {count} سجل")
            except Exception as e:
                print(f"  ❌ خطأ في جدول {table}: {e}")
        
        # اختبار دالة get_classes
        print(f"\n🧪 اختبار دالة get_classes:")
        try:
            classes = db_manager.get_classes()
            print(f"  ✅ get_classes(): {len(classes)} فصل")
            
            if classes:
                print("  📝 عينة من الفصول:")
                for i, cls in enumerate(classes[:3]):
                    print(f"    {i+1}. {cls.get('name', 'بدون اسم')} | {cls.get('section_name', 'بدون شعبة')} | {cls.get('subject_name', 'بدون مادة')}")
            else:
                print("  ⚠️ لا توجد فصول في قاعدة البيانات")
                
        except Exception as e:
            print(f"  ❌ خطأ في get_classes(): {e}")
            import traceback
            traceback.print_exc()
        
        # اختبار دالة get_sections
        print(f"\n🧪 اختبار دالة get_sections:")
        try:
            sections = db_manager.get_sections()
            print(f"  ✅ get_sections(): {len(sections)} شعبة")
            
            if sections:
                print("  📝 عينة من الشعب:")
                for i, section in enumerate(sections[:3]):
                    print(f"    {i+1}. {section.get('name', 'بدون اسم')} | {section.get('department_name', 'بدون قسم')}")
            else:
                print("  ⚠️ لا توجد شعب في قاعدة البيانات")
                
        except Exception as e:
            print(f"  ❌ خطأ في get_sections(): {e}")
            import traceback
            traceback.print_exc()
        
        # اختبار دالة get_subjects
        print(f"\n🧪 اختبار دالة get_subjects:")
        try:
            subjects = db_manager.get_subjects()
            print(f"  ✅ get_subjects(): {len(subjects)} مادة")
            
            if subjects:
                print("  📝 عينة من المواد:")
                for i, subject in enumerate(subjects[:3]):
                    print(f"    {i+1}. {subject.get('name', 'بدون اسم')} | {subject.get('code', 'بدون رمز')}")
            else:
                print("  ⚠️ لا توجد مواد في قاعدة البيانات")
                
        except Exception as e:
            print(f"  ❌ خطأ في get_subjects(): {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"  ❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return
    
    # 2. اختبار إنشاء واجهة الفصول
    print(f"\n🎨 اختبار إنشاء واجهة الفصول:")
    
    try:
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.title("اختبار واجهة الفصول")
        root.geometry("1200x800")
        root.withdraw()  # إخفاء النافذة
        
        print("  ✅ إنشاء النافذة الرئيسية نجح")
        
        # محاولة إنشاء تبويب الفصول
        classes_tab = ClassesTab(root, db_manager)
        print("  ✅ إنشاء تبويب الفصول نجح")
        
        # اختبار تحديث البيانات
        classes_tab.refresh_data()
        print("  ✅ تحديث بيانات الفصول نجح")
        
        # اختبار تحميل الشعب والمواد
        classes_tab.load_sections_and_subjects()
        print("  ✅ تحميل الشعب والمواد نجح")
        
        root.destroy()
        
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء واجهة الفصول: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. اختبار التطبيق الرئيسي
    print(f"\n🏠 اختبار التطبيق الرئيسي:")

    try:
        from gui.main_window import MainWindow

        # إنشاء التطبيق
        app = MainWindow()
        print("  ✅ إنشاء التطبيق الرئيسي نجح")

        # محاولة الدخول لتبويب الفصول
        print("  🎯 محاولة الدخول لتبويب الفصول...")
        app.show_section('classes')
        print("  ✅ الدخول لتبويب الفصول نجح")

        # التحقق من إنشاء تبويب الفصول
        if 'classes' in app.sections:
            classes_section = app.sections['classes']
            if classes_section['widget'] is not None:
                print("  ✅ تم إنشاء تبويب الفصول بنجاح")

                # اختبار تحديث البيانات
                if hasattr(classes_section['widget'], 'refresh_data'):
                    classes_section['widget'].refresh_data()
                    print("  ✅ تحديث بيانات الفصول في التطبيق نجح")
                else:
                    print("  ⚠️ دالة refresh_data غير موجودة في تبويب الفصول")
            else:
                print("  ❌ فشل في إنشاء تبويب الفصول")
        else:
            print("  ❌ تبويب الفصول غير موجود في التطبيق")

        # إغلاق التطبيق
        app.root.destroy()

    except Exception as e:
        print(f"  ❌ خطأ في التطبيق الرئيسي: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. التوصيات
    print(f"\n💡 التوصيات:")
    print("  1. تأكد من وجود بيانات في جداول classes و sections و subjects")
    print("  2. تحقق من صحة الاستعلامات في دالة get_classes()")
    print("  3. تأكد من أن واجهة الفصول تتعامل مع البيانات الفارغة بشكل صحيح")
    print("  4. فحص رسائل الخطأ في وحدة التحكم")
    
    print(f"\n" + "=" * 60)
    print("✅ انتهى تشخيص مشكلة تحميل الفصول")
    
    # إغلاق قاعدة البيانات
    db_manager.close()

if __name__ == "__main__":
    debug_classes_loading()
