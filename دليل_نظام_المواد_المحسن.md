# دليل نظام المواد المحسن

## نظرة عامة
تم تطوير نظام المواد ليدعم المتطلبات التالية:

### 1. المواد الثقافية
- **موجودة في جميع التخصصات**
- **منها ما يُضاف للمجموع** (مثل: اللغة العربية، الإنجليزية، الرياضيات)
- **منها ما لا يُضاف للمجموع** (مثل: التربية الدينية، التربية الرياضية)

### 2. المواد التخصصية
- **مختلفة حسب التخصص**
- **عدد وحدات مرن**: 
  - تخصص 5 وحدات
  - تخصص 7 وحدات  
  - تخصص 9 وحدات

## الميزات الجديدة

### حقول المادة المحسنة:
1. **اسم المادة** - مطلوب
2. **رمز المادة** - اختياري (مثل: CS101, AR101)
3. **نوع المادة** - مطلوب (ثقافية / تخصصية)
4. **القسم** - اختياري للمواد الثقافية، مطلوب للتخصصية
5. **يُضاف للمجموع** - مطلوب (نعم / لا)
6. **عدد الوحدات** - مطلوب (1-9 وحدات)
7. **إجمالي الدرجة** - اختياري (افتراضي: 100)
8. **أعمال السنة والترمين** - كما هو
9. **وصف المادة** - اختياري

## المواد الثقافية الافتراضية

تم إضافة المواد التالية تلقائياً:

### مواد تُضاف للمجموع:
- **اللغة العربية** (AR101) - 2 وحدة - 100 درجة
- **اللغة الإنجليزية** (EN101) - 2 وحدة - 100 درجة  
- **الرياضيات** (MA101) - 3 وحدات - 100 درجة
- **الفيزياء** (PH101) - 2 وحدة - 100 درجة
- **الكيمياء** (CH101) - 2 وحدة - 100 درجة

### مواد لا تُضاف للمجموع:
- **التربية الدينية** (RE101) - 1 وحدة - 50 درجة
- **التربية الرياضية** (PE101) - 1 وحدة - 50 درجة

## كيفية إضافة مادة جديدة

### 1. المواد الثقافية:
```
اسم المادة: التاريخ
رمز المادة: HI101
نوع المادة: ثقافية
القسم: [اتركه فارغ أو اختر "جميع التخصصات"]
يُضاف للمجموع: نعم
عدد الوحدات: 2
إجمالي الدرجة: 100
```

### 2. المواد التخصصية:
```
اسم المادة: برمجة الحاسوب
رمز المادة: CS201
نوع المادة: تخصصية
القسم: الحاسب الآلي
يُضاف للمجموع: نعم
عدد الوحدات: 3
إجمالي الدرجة: 100
```

## نظام الوحدات المرن

### تخصص 5 وحدات (مثال):
- مادة تخصصية 1: 2 وحدة
- مادة تخصصية 2: 3 وحدات
- **المجموع**: 5 وحدات

### تخصص 7 وحدات (مثال):
- مادة تخصصية 1: 3 وحدات
- مادة تخصصية 2: 2 وحدة
- مادة تخصصية 3: 2 وحدة
- **المجموع**: 7 وحدات

### تخصص 9 وحدات (مثال):
- مادة تخصصية 1: 3 وحدات
- مادة تخصصية 2: 3 وحدات
- مادة تخصصية 3: 3 وحدات
- **المجموع**: 9 وحدات

## حل مشكلة إضافة المواد

تم حل المشاكل التالية:
1. ✅ **تضارب هيكل قاعدة البيانات** - تم توحيد الأعمدة
2. ✅ **دعم المواد الثقافية المشتركة** - تعمل عبر جميع التخصصات
3. ✅ **نظام الوحدات المرن** - يدعم 5، 7، 9 وحدات
4. ✅ **خاصية "يُضاف للمجموع"** - للتحكم في احتساب الدرجات
5. ✅ **التحقق الديناميكي من الأعمدة** - يعمل مع قواعد البيانات القديمة والجديدة

## ملاحظات مهمة

1. **المواد الثقافية** تظهر لجميع الطلاب بغض النظر عن التخصص
2. **المواد التخصصية** تظهر فقط لطلاب التخصص المحدد
3. **عدد الوحدات** يمكن تخصيصه حسب احتياجات كل تخصص
4. **النظام متوافق** مع قواعد البيانات الموجودة مسبقاً

## الخطوات التالية

1. اختبار إضافة المواد من خلال الواجهة
2. إضافة المواد التخصصية لكل قسم
3. ربط المواد بالطلاب حسب التخصص
4. تطوير نظام التقارير ليعكس النظام الجديد
