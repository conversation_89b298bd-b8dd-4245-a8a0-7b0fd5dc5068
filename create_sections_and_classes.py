#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء شعب وفصول تجريبية
Create sample sections and classes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def create_sections_and_classes():
    """إنشاء شعب وفصول تجريبية"""
    
    print("🏗️ إنشاء شعب وفصول تجريبية")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    db_manager.connect()
    cursor = db_manager.connection.cursor()
    
    try:
        # 1. الحصول على الأقسام الموجودة
        cursor.execute("SELECT id, name FROM departments")
        departments = cursor.fetchall()
        
        print(f"📋 الأقسام الموجودة: {len(departments)}")
        for dept in departments:
            print(f"  • {dept[1]} (ID: {dept[0]})")
        
        # 2. إنشاء شعب لكل قسم
        print(f"\n🏫 إنشاء الشعب:")
        
        sections_data = []
        for dept_id, dept_name in departments:
            # إنشاء 3 شعب لكل قسم
            for i in range(1, 4):
                section_name = f"شعبة {i} - {dept_name}"
                academic_year = "2024-2025"
                
                try:
                    cursor.execute("""
                        INSERT INTO sections (name, department_id, academic_year, description)
                        VALUES (?, ?, ?, ?)
                    """, (section_name, dept_id, academic_year, f"شعبة رقم {i} لقسم {dept_name}"))
                    
                    section_id = cursor.lastrowid
                    sections_data.append((section_id, section_name, dept_id, dept_name))
                    print(f"  ✅ {section_name} (ID: {section_id})")
                    
                except Exception as e:
                    print(f"  ❌ خطأ في إنشاء {section_name}: {e}")
        
        db_manager.connection.commit()
        
        # 3. الحصول على المواد الموجودة
        cursor.execute("SELECT id, name, is_cultural FROM subjects")
        subjects = cursor.fetchall()
        
        print(f"\n📚 المواد الموجودة: {len(subjects)}")
        cultural_subjects = [s for s in subjects if s[2] == 1]
        specialized_subjects = [s for s in subjects if s[2] == 0]
        
        print(f"  • مواد ثقافية: {len(cultural_subjects)}")
        print(f"  • مواد تخصصية: {len(specialized_subjects)}")
        
        # 4. إنشاء فصول
        print(f"\n🎓 إنشاء الفصول:")
        
        class_counter = 1
        
        for section_id, section_name, dept_id, dept_name in sections_data:
            # إضافة فصول للمواد الثقافية (لجميع الشعب)
            for subject_id, subject_name, _ in cultural_subjects[:5]:  # أول 5 مواد ثقافية
                class_name = f"فصل {subject_name} - {section_name}"
                
                try:
                    cursor.execute("""
                        INSERT INTO classes (name, section_id, subject_id, teacher_name, 
                                           schedule, room_number, semester, academic_year, description)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        class_name,
                        section_id,
                        subject_id,
                        f"د. مدرس {subject_name}",
                        f"الأحد والثلاثاء {8 + (class_counter % 4)}:00-{10 + (class_counter % 4)}:00",
                        f"قاعة {100 + class_counter}",
                        "الفصل الأول",
                        "2024-2025",
                        f"فصل {subject_name} للشعبة {section_name}"
                    ))
                    
                    print(f"  ✅ {class_name}")
                    class_counter += 1
                    
                except Exception as e:
                    print(f"  ❌ خطأ في إنشاء {class_name}: {e}")
            
            # إضافة فصول للمواد التخصصية (حسب القسم)
            dept_specialized = [s for s in specialized_subjects if s[1].find(dept_name.split()[0]) != -1]
            if not dept_specialized:
                dept_specialized = specialized_subjects[:2]  # أول مادتين تخصصيتين كمثال
            
            for subject_id, subject_name, _ in dept_specialized[:3]:  # أول 3 مواد تخصصية
                class_name = f"فصل {subject_name} - {section_name}"
                
                try:
                    cursor.execute("""
                        INSERT INTO classes (name, section_id, subject_id, teacher_name, 
                                           schedule, room_number, semester, academic_year, description)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        class_name,
                        section_id,
                        subject_id,
                        f"د. مدرس {subject_name}",
                        f"الاثنين والأربعاء {8 + (class_counter % 4)}:00-{10 + (class_counter % 4)}:00",
                        f"قاعة {100 + class_counter}",
                        "الفصل الأول",
                        "2024-2025",
                        f"فصل {subject_name} للشعبة {section_name}"
                    ))
                    
                    print(f"  ✅ {class_name}")
                    class_counter += 1
                    
                except Exception as e:
                    print(f"  ❌ خطأ في إنشاء {class_name}: {e}")
        
        db_manager.connection.commit()
        
        # 5. عرض الإحصائيات النهائية
        print(f"\n📊 الإحصائيات النهائية:")
        
        cursor.execute("SELECT COUNT(*) FROM sections")
        sections_count = cursor.fetchone()[0]
        print(f"  • إجمالي الشعب: {sections_count}")
        
        cursor.execute("SELECT COUNT(*) FROM classes")
        classes_count = cursor.fetchone()[0]
        print(f"  • إجمالي الفصول: {classes_count}")
        
        # عرض عينة من الفصول
        cursor.execute("""
            SELECT c.name, sec.name, sub.name, c.teacher_name
            FROM classes c
            JOIN sections sec ON c.section_id = sec.id
            JOIN subjects sub ON c.subject_id = sub.id
            LIMIT 5
        """)
        sample_classes = cursor.fetchall()
        
        print(f"\n📝 عينة من الفصول المنشأة:")
        for cls in sample_classes:
            print(f"  • {cls[0]} | {cls[1]} | {cls[2]} | {cls[3]}")
        
        print(f"\n" + "=" * 50)
        print("✅ تم إنشاء الشعب والفصول بنجاح!")
        print("🎯 يمكنك الآن الدخول على تبويب الفصول وستجد البيانات محملة")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    create_sections_and_classes()
