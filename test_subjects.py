#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام المواد المحسن
Test enhanced subjects system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_subjects_system():
    """اختبار نظام المواد المحسن"""
    
    # إنشاء اتصال بقاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # إعادة إنشاء قاعدة البيانات
    print("إعادة إنشاء قاعدة البيانات...")
    db_manager.create_tables()
    
    # التحقق من هيكل جدول المواد
    cursor = db_manager.connection.cursor()
    cursor.execute("PRAGMA table_info(subjects)")
    columns = cursor.fetchall()
    
    print("هيكل جدول المواد:")
    print("=" * 60)
    for column in columns:
        print(f"العمود: {column[1]:<20} النوع: {column[2]:<15} مطلوب: {'نعم' if column[3] else 'لا'}")
    
    print("\n" + "=" * 60)
    
    # إضافة البيانات الافتراضية
    print("إضافة البيانات الافتراضية...")
    db_manager.insert_default_data()
    
    # عرض المواد المضافة
    cursor.execute("SELECT * FROM subjects")
    subjects = cursor.fetchall()
    
    print(f"\nتم إضافة {len(subjects)} مادة:")
    print("=" * 60)
    for subject in subjects:
        print(f"المادة: {subject[1]}")
        if len(subject) > 4:
            print(f"  - الرمز: {subject[2] if subject[2] else 'غير محدد'}")
            print(f"  - النوع: {subject[4] if len(subject) > 4 else 'غير محدد'}")
            print(f"  - ثقافية: {'نعم' if len(subject) > 5 and subject[5] else 'لا'}")
            print(f"  - تُضاف للمجموع: {'نعم' if len(subject) > 6 and subject[6] else 'لا'}")
            print(f"  - عدد الوحدات: {subject[7] if len(subject) > 7 else 'غير محدد'}")
        print("-" * 40)
    
    # اختبار إضافة مادة تخصصية
    print("\nاختبار إضافة مادة تخصصية...")
    try:
        # الحصول على معرف قسم الحاسب الآلي
        cursor.execute("SELECT id FROM departments WHERE name LIKE '%Computer%' OR name LIKE '%حاسب%' LIMIT 1")
        dept_result = cursor.fetchone()
        dept_id = dept_result[0] if dept_result else 1
        
        # إضافة مادة تخصصية
        test_subject = {
            'name': 'برمجة الحاسوب',
            'code': 'CS201',
            'department_id': dept_id,
            'subject_category': 'تخصصية',
            'is_cultural': 0,
            'include_in_total': 1,
            'units': 3,
            'total_degree': 100,
            'description': 'مادة برمجة الحاسوب الأساسية'
        }
        
        # التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(subjects)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # بناء استعلام الإدراج
        available_fields = []
        values = []
        
        for field, value in test_subject.items():
            if field in columns and value is not None:
                available_fields.append(field)
                values.append(value)
        
        if available_fields:
            placeholders = ', '.join(['?' for _ in available_fields])
            fields_str = ', '.join(available_fields)
            query = f"INSERT INTO subjects ({fields_str}) VALUES ({placeholders})"
            
            cursor.execute(query, values)
            db_manager.connection.commit()
            
            print("تم إضافة المادة التخصصية بنجاح!")
        else:
            print("لا توجد حقول صالحة للإدراج")
            
    except Exception as e:
        print(f"خطأ في إضافة المادة التخصصية: {e}")
    
    # عرض جميع المواد النهائية
    cursor.execute("SELECT COUNT(*) FROM subjects")
    total_subjects = cursor.fetchone()[0]
    print(f"\nإجمالي المواد في النظام: {total_subjects}")
    
    db_manager.close()
    print("\nتم الانتهاء من الاختبار بنجاح!")

if __name__ == "__main__":
    test_subjects_system()
